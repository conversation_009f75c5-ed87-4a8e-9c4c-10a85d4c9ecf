/* Global Resets & Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  line-height: 1.6;
  background-color: #ffffff; /* 纯白背景 */
  color: #333;
  font-size: 16px;
}

.container {
  width: 100%;
  margin: 0;
  padding: 20px;
}

h1, h2, h3 {
  color: #2c3e50; /* Darker, more professional heading color */
  margin-bottom: 0.75em;
}

h1 {
  font-size: 2em;
  text-align: center;
  margin-bottom: 30px;
}

h2 {
  font-size: 1.5em;
}

h3 {
  font-size: 1.2em;
}

/* Card styling for content blocks */
.card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 25px 30px;
  margin-bottom: 20px;
}

/* Login Page Specifics (index.html & admin.html login) */
.login-body {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

.login-container-wrapper {
  width: 100%;
  max-width: 420px; /* Consistent width for login forms */
}

/* yanshiye页面专用容器 - 无宽度限制 */
.yanshiye-container {
  width: 100%;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.login-container-wrapper h1 {
  color: #3498db; /* Primary color for login title */
}

.admin-login-box {
  max-width: 400px;
  margin: 20px auto;
}

/* Tabs */
.tabs {
  display: flex;
  margin-bottom: 25px;
  border-bottom: 1px solid #e0e0e0;
}

.tab {
  padding: 12px 20px;
  cursor: pointer;
  font-weight: 500;
  color: #555;
  border-bottom: 3px solid transparent;
  transition: color 0.3s, border-color 0.3s;
  text-align: center;
  flex-grow: 1; /* Make tabs expand */
}

.tab:hover {
  color: #3498db;
}

.tab.active {
  color: #3498db; /* Primary color */
  border-bottom-color: #3498db;
}

/* Forms */
.form-content {
  padding-top: 10px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: #495057;
}

input[type="text"],
input[type="password"],
input[type="number"],
select.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 1em;
  transition: border-color 0.2s, box-shadow 0.2s;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
select.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Buttons */
.button {
  padding: 10px 20px;
  font-size: 1em;
  font-weight: 500;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  text-align: center;
  display: inline-block;
}

.button:hover {
  opacity: 0.9;
}
.button:active {
  transform: translateY(1px);
}

.button-primary {
  background-color: #3498db; /* Primary color */
  color: white;
}
.button-primary:hover {
  background-color: #2980b9; /* Darker shade for hover */
}

.button-secondary {
  background-color: #e0e0e0;
  color: #333;
}
.button-secondary:hover {
  background-color: #bdbdbd;
}

.button-danger {
  background-color: #e74c3c; /* Red for danger */
  color: white;
}
.button-danger:hover {
  background-color: #c0392b;
}

.button-success {
  background-color: #28a745;
  color: white;
}

.button-success:hover {
  background-color: #218838;
}

.button-warning {
  background-color: #f39c12; /* Orange for warning */
  color: white;
}
.button-warning:hover {
  background-color: #e67e22;
}

.button-info {
  background-color: #17a2b8; /* Info blue */
  color: white;
}
.button-info:hover {
  background-color: #138496;
}

/* 按钮禁用状态 */
.button:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
  opacity: 0.6;
}

.button:disabled:hover {
  background-color: #95a5a6;
}

/* 定时任务测试样式 */
.task-status {
  padding: 8px 12px;
  border-radius: 4px;
  font-weight: bold;
  display: inline-block;
  min-width: 80px;
  text-align: center;
}

.task-status.waiting {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.task-status.running {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.task-status.completed {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.task-status.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.task-logs {
  background-color: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  padding: 15px;
  border-radius: 4px;
  height: 300px;
  overflow-y: auto;
  border: 1px solid #444;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.task-logs .log-entry {
  margin-bottom: 2px;
  line-height: 1.4;
}

.task-logs .log-timestamp {
  color: #569cd6;
}

.task-logs .log-info {
  color: #4ec9b0;
}

.task-logs .log-warning {
  color: #dcdcaa;
}

.task-logs .log-error {
  color: #f44747;
}

.task-logs .log-success {
  color: #4fc1e9;
}

/* API压力测试样式 */
.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.test-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-item label {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 5px;
  font-weight: 600;
}

.stat-item span {
  font-size: 18px;
  font-weight: bold;
  color: #495057;
}

.success-count {
  color: #28a745 !important;
}

.error-count {
  color: #dc3545 !important;
}

.test-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px !important;
}

.test-status.running {
  background-color: #d1ecf1;
  color: #0c5460;
}

.test-status.completed {
  background-color: #d4edda;
  color: #155724;
}

.test-status.stopped {
  background-color: #f8d7da;
  color: #721c24;
}

.pressure-test-logs {
  background-color: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  padding: 15px;
  border-radius: 4px;
  height: 400px;
  overflow-y: auto;
  border: 1px solid #444;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.pressure-test-logs .log-entry {
  margin-bottom: 1px;
  line-height: 1.3;
}

.pressure-test-logs .log-request {
  color: #569cd6;
}

.pressure-test-logs .log-response-success {
  color: #4ec9b0;
}

.pressure-test-logs .log-response-error {
  color: #f44747;
}

.pressure-test-logs .log-timing {
  color: #dcdcaa;
}

.pressure-test-logs .log-summary {
  color: #4fc1e9;
  font-weight: bold;
}

/* API压力测试样式 */
.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.test-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-item label {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 5px;
  font-weight: 600;
}

.stat-item span {
  font-size: 18px;
  font-weight: bold;
  color: #495057;
}

.success-count {
  color: #28a745 !important;
}

.error-count {
  color: #dc3545 !important;
}

.test-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px !important;
}

.test-status.running {
  background-color: #d1ecf1;
  color: #0c5460;
}

.test-status.completed {
  background-color: #d4edda;
  color: #155724;
}

.test-status.stopped {
  background-color: #f8d7da;
  color: #721c24;
}

.pressure-test-logs {
  background-color: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  padding: 15px;
  border-radius: 4px;
  height: 400px;
  overflow-y: auto;
  border: 1px solid #444;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.pressure-test-logs .log-entry {
  margin-bottom: 1px;
  line-height: 1.3;
}

.pressure-test-logs .log-request {
  color: #569cd6;
}

.pressure-test-logs .log-response-success {
  color: #4ec9b0;
}

.pressure-test-logs .log-response-error {
  color: #f44747;
}

.pressure-test-logs .log-timing {
  color: #dcdcaa;
}

.pressure-test-logs .log-summary {
  color: #4fc1e9;
  font-weight: bold;
}

.button-full-width {
  width: 100%;
  padding: 12px;
}

.button-small {
  padding: 6px 12px;
  font-size: 0.85em;
}

/* 按钮组样式 */
.update-buttons-group, .test-buttons-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 8px 0;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.update-buttons-group {
  border-left: 4px solid #17a2b8;
}

.test-buttons-group {
  border-left: 4px solid #f39c12;
}

/* Messages (Alerts) */
.message {
  padding: 12px 18px;
  margin: 20px 0;
  border-radius: 6px;
  text-align: center;
  font-size: 0.95em;
}

.message-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.message-warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

.message-info { /* For general info, like logout message */
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.hidden {
  display: none !important;
}

/* User Logged In Section (index.html) */
#user-container h2 {
  margin-top: 0;
  color: #3498db;
}
.user-info {
  background-color: #eaf5ff; /* Light blue tint */
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 25px;
}
.user-info p {
  margin-bottom: 10px;
  font-size: 1.05em;
}
.user-info strong {
  color: #2c3e50;
}

#activation-status {
  font-weight: bold;
}
/* activation status badges are handled by general .status-badge */

.activation-section {
  margin: 25px 0;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #f9f9f9;
}
.activation-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
}
.inline-form-group {
  display: flex;
  align-items: center;
  gap: 10px;
}
.form-control-flex {
  flex: 1;
}
#activate-btn {
  padding: 12px 20px; /* Match input height */
}


/* Modal (Force Login) */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 15px;
}

/* 修改密码对话框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-content {
  background-color: white;
  padding: 25px;
  border-radius: 8px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  animation: modal-fade-in 0.3s ease-out;
  position: relative;
}

/* 账号信息模态框特殊样式 - 更宽以适应JSON数据 */
#account-info-modal .modal-content {
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  #account-info-modal .modal-content {
    width: 95%;
    max-width: none;
    margin: 10px;
  }
}

/* JSON数据输入框样式 - 使用更高优先级 */
#account-json-data.form-control {
  width: 100% !important;
  min-height: 400px;
  max-width: none !important;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  resize: vertical;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  box-sizing: border-box;
}

/* 账号信息模态框内的表单组样式 */
#account-info-modal .form-group {
  margin-bottom: 20px;
}

#account-info-modal .form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

/* 确认按钮样式 */
#account-info-modal .button-full-width {
  width: 100%;
  margin-top: 15px;
}

.close-modal,
.close-account-info-modal,
.close-transfer-modal {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 24px;
  font-weight: bold;
  color: #aaa;
  cursor: pointer;
}

.close-modal:hover,
.close-account-info-modal:hover,
.close-transfer-modal:hover {
  color: #333;
}

@keyframes modal-fade-in {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
}
.modal-title {
  font-size: 1.25em;
  margin-bottom: 0;
  color: #333;
}
.modal-body {
  padding: 20px;
  line-height: 1.7;
}
.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Admin Page Specifics */
.admin-body {
  background-color: #eef1f5; /* Slightly different bg for admin area */
}

.admin-main-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  padding: 25px 30px;
  margin-top: 20px;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}
.admin-header h1 {
    margin-bottom: 0; /* Reset margin from global h1 */
}

.admin-tabs {
  margin-bottom: 30px;
}

.tab-panel {
  padding-top: 10px;
}
.tab-panel.hidden { /* Ensure panels are truly hidden */
    display: none !important;
}


.panel-controls {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  align-items: center;
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
}
.panel-controls .form-control { /* Search input */
  max-width: 300px;
  flex-grow: 1;
}

.filter-bar {
    display: flex;
    gap: 15px;
    align-items: center;
}
.filter-bar select.form-control {
    min-width: 150px; /* Give selects some base width */
}


.table-container {
  overflow-x: auto;
  margin-top: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  width: 100%;
}

table {
  width: 100%;
  min-width: 1400px; /* 确保表格有足够宽度显示所有列 */
  border-collapse: collapse;
}

th, td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid #e9ecef; /* Lighter border */
  vertical-align: middle;
  white-space: nowrap; /* 防止内容换行 */
  overflow: hidden;
  text-overflow: ellipsis; /* 超长内容显示省略号 */
}

th {
  background-color: #f8f9fa; /* Light grey header */
  font-weight: 600;
  color: #495057;
  font-size: 0.9em;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

tr:hover {
  background-color: #f1f3f5; /* Subtle hover */
}
td .button { /* Buttons inside table cells */
  margin-right: 5px;
  padding: 4px 8px; /* 减小按钮内边距 */
  font-size: 12px; /* 减小字体大小 */
  white-space: nowrap; /* 按钮文字不换行 */
}
td .button:last-child {
  margin-right: 0;
}

/* 操作列样式 */
.action-column {
  min-width: 120px; /* 确保操作列有足够宽度 */
  white-space: nowrap;
}

/* 表格列宽度优化 */
.phone-column {
  min-width: 120px;
}

.platform-column {
  min-width: 120px;
}

.username-column {
  min-width: 100px;
}

.login-type-column {
  min-width: 80px;
}

.status-column {
  min-width: 80px;
}

.stats-column {
  min-width: 80px;
  text-align: right;
}

.date-column {
  min-width: 100px;
}

.text-center {
    text-align: center !important;
}

/* Status Badges (General) */
.status-badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 15px; /* Pill shape */
  font-size: 0.8em;
  font-weight: 500;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
}

.status-online, .status-activated, .status-unused {
  background-color: #27ae60;
  color: white;
}

.status-offline, .status-inactive, .status-used {
  background-color: #95a5a6;
  color: white;
}

.status-expired {
  background-color: #e74c3c;
  color: white;
}

.status-offline { /* More subdued for offline */
  background-color: #7f8c8d;
}

.status-main-account {
  background-color: #3498db;
  color: white;
}

.status-sub-account {
  background-color: #f39c12;
  color: white;
}

/* 主仪表板布局 */
.main-dashboard {
  width: 100%;
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 顶部用户信息栏 */
.top-user-bar {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info-summary h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
}

.user-basic-info {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.user-phone-display {
  font-weight: 500;
  color: #666;
}

.top-actions {
  display: flex;
  gap: 10px;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  gap: 20px;
}

/* 左侧功能面板 */
.left-sidebar {
  flex: 0 0 280px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.function-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.function-card h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 18px;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

.function-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* 右侧主要区域 */
.right-main {
  flex: 1;
  min-width: 0;
}

/* 平台账号区域 */
.platform-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.section-header {
  padding: 20px;
  border-bottom: 2px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.section-header h3 {
  margin: 0;
  color: #333;
  font-size: 20px;
}

.search-controls {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  min-width: 250px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

/* 平台账号表格样式 */
.platform-table-container {
  overflow-x: auto;
}

.platform-table {
  width: 100%;
  min-width: 1000px;
  border-collapse: collapse;
  background: white;
}

.platform-table th {
  background-color: #f8f9fa;
  color: #333;
  font-weight: 600;
  padding: 15px 12px;
  text-align: left;
  border-bottom: 2px solid #e9ecef;
  font-size: 14px;
  white-space: nowrap;
  position: sticky;
  top: 0;
  z-index: 10;
}

.platform-table td {
  padding: 15px 12px;
  border-bottom: 1px solid #e9ecef;
  font-size: 13px;
  color: #555;
  vertical-align: middle;
}

.platform-table tbody tr:hover {
  background-color: #f8f9fa;
}

.no-data {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 40px 20px;
}

.owner-cell {
  font-weight: 500;
  color: #666;
}

.phone-cell {
  font-weight: 600;
  color: #007bff;
}

.number-cell {
  text-align: right;
  font-family: 'Courier New', monospace;
}

.withdraw-amount-cell {
  text-align: right;
  font-family: 'Courier New', monospace;
  color: #28a745;
  font-weight: 600;
}

.time-cell {
  font-size: 12px;
  color: #666;
}

.action-cell {
  text-align: center;
  white-space: nowrap;
}

.action-btn {
  padding: 4px 8px;
  margin: 0 2px;
  border: none;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.edit-btn {
  background-color: #007bff;
  color: white;
}

.edit-btn:hover {
  background-color: #0056b3;
}

.delete-btn {
  background-color: #dc3545;
  color: white;
}

.delete-btn:hover {
  background-color: #c82333;
}

.verification-badge {
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.verification-badge.verified {
  background-color: #d4edda;
  color: #155724;
}

.verification-badge.unverified {
  background-color: #f8d7da;
  color: #721c24;
}

.account-status {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.account-status.status-normal {
  background-color: #d4edda;
  color: #155724;
}

.account-status.status-abnormal {
  background-color: #f8d7da;
  color: #721c24;
}

.account-status.status-offline {
  background-color: #fff3cd;
  color: #856404;
}

/* 子账号容器样式 */
.sub-accounts-container {
  margin-top: 15px;
}

.no-sub-accounts {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .left-sidebar {
    flex: none;
    width: 100%;
    flex-direction: row;
    overflow-x: auto;
  }

  .function-card {
    min-width: 250px;
  }

  .platform-table {
    font-size: 12px;
    min-width: 800px;
  }

  .platform-table th,
  .platform-table td {
    padding: 10px 8px;
  }

  /* 调整表格最小宽度 */
  table {
    min-width: 1200px;
  }
}

@media (max-width: 768px) {
  .main-dashboard {
    padding: 10px;
  }

  .top-user-bar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .user-basic-info {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
  }

  .search-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    min-width: auto;
  }

  .left-sidebar {
    flex-direction: column;
  }

  .platform-table {
    font-size: 11px;
    min-width: 600px;
  }

  .platform-table th,
  .platform-table td {
    padding: 8px 4px;
  }

  .action-btn {
    padding: 2px 4px;
    font-size: 10px;
  }
}


.generation-card {
  margin-top: 25px;
  margin-bottom: 25px;
  max-width: 500px; /* Constrain width of generation form */
}
.generation-card .form-group {
    display: flex;
    align-items: center;
}
.generation-card .form-group label {
    flex-basis: 80px; /* Fixed width for labels */
    margin-bottom: 0; /* Reset margin from global label */
    text-align: right;
    padding-right: 15px;
}
.generation-card .form-control {
    flex-grow: 1;
}
.form-actions {
  margin-top: 20px;
  padding-left: 95px; /* Align with input fields (label width + padding) */
}

.code-box {
  padding: 15px;
  background-color: #f1f3f5;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  margin-top: 20px;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
  font-size: 0.9em;
}

/* Admin login message area specific styling */
#admin-login-message {
  margin-top: 15px; /* Space between button and message */
  margin-bottom: 0;
}

/* Admin general message area */
.admin-message-area {
  margin-top: 0; /* No top margin if it's first in its container */
  margin-bottom: 20px;
}

/* 账号类型样式 */
.account-type-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  min-width: 60px;
}

.type-main {
  background-color: #3498db;
  color: white;
}

.type-sub {
  background-color: #95a5a6;
  color: white;
}

/* 账号行样式 */
.main-account-row {
  background-color: #ffffff;
}

.sub-account-row {
  background-color: #f8f9fa;
  border-left: 3px solid #95a5a6;
}

.sub-account-row td:first-child {
  padding-left: 20px;
  color: #6c757d;
}

/* 演示页面子账号管理样式 */
.sub-account-section {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.sub-account-section h3 {
  margin-bottom: 15px;
  color: #2c3e50;
}

.sub-account-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.sub-account-controls .button {
  padding: 8px 16px;
  font-size: 14px;
}

.sub-accounts-container {
  max-height: 300px;
  overflow-y: auto;
}

.sub-account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin-bottom: 8px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.sub-account-info {
  flex: 1;
}

.sub-account-info .phone {
  font-weight: bold;
  color: #2c3e50;
}

.sub-account-info .created-time {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.sub-account-actions {
  display: flex;
  gap: 5px;
}

.sub-account-actions .button {
  padding: 4px 8px;
  font-size: 12px;
}

.no-sub-accounts {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

/* 批量处理进度样式 */
.batch-progress-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.batch-progress-container h4 {
  margin-bottom: 15px;
  color: #495057;
}

.progress-bar-container {
  margin-bottom: 15px;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  transition: width 0.3s ease;
  border-radius: 10px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  font-weight: 500;
  color: #495057;
}

.batch-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
}

.stat-item {
  background: white;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  font-size: 14px;
  font-weight: 500;
}

.current-account {
  background: #e3f2fd;
  padding: 10px;
  border-radius: 4px;
  border-left: 4px solid #2196f3;
  font-weight: 500;
  color: #1976d2;
}

/* 更新按钮组样式优化 */
.update-buttons-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.update-buttons-group .button {
  flex: 1;
  min-width: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .batch-stats {
    flex-direction: column;
  }

  .stat-item {
    text-align: center;
  }

  .update-buttons-group {
    flex-direction: column;
  }

  .update-buttons-group .button {
    min-width: auto;
  }
}