# 用户批量更新平台账号API使用示例

## 接口信息

**接口地址**: `POST /api/user/batch-update-platform-accounts`

**权限要求**: 需要有效的用户会话

## 请求格式

```json
{
  "sessionId": "用户会话ID",
  "user_id": 1,
  "platformAccountsData": {
    "***********": {
      "phone": "***********",
      "login_type": "password",
      "team_tag": "团队A",
      "data_update_time": "2024-07-22T10:00:00.000Z",
      "login_time": "2024-07-22T09:00:00.000Z",
      "username": "用户名1",
      "sessionid": "session123",
      "homepage_url": "https://example.com/user1",
      "is_verified": "true",
      "drafts_count": "5",
      "stats": {
        "followers": "1000",
        "total_reads": "50000",
        "total_income": "1500.00",
        "yesterday_reads": "200",
        "yesterday_income": "50.00",
        "credit_score": "95"
      },
      "account_status": "active",
      "owner_id": 1,
      "current_holder_id": 1,
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-07-22T10:00:00.000Z"
    },
    "***********": {
      "phone": "***********",
      "login_type": "sms",
      "team_tag": "团队B",
      "username": "用户名2",
      "stats": {
        "followers": "800",
        "total_reads": "30000",
        "total_income": "1200.00",
        "yesterday_reads": "150",
        "yesterday_income": "40.00",
        "credit_score": "88"
      },
      "account_status": "active",
      "owner_id": 1,
      "current_holder_id": 2
    }
  }
}
```

## 响应格式

```json
{
  "success": true,
  "message": "成功更新 2 个平台账号数据"
}
```

## JavaScript示例

```javascript
const API_URL = 'https://your-domain.com';

async function batchUpdatePlatformAccounts(sessionId, platformAccountsData) {
    try {
        const response = await fetch(`${API_URL}/api/user/batch-update-platform-accounts`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sessionId: sessionId,
                user_id: 1, // 用户ID
                platformAccountsData: platformAccountsData
            })
        });
        
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('批量更新平台账号失败:', error);
        throw error;
    }
}

// 使用示例
async function updateMultiplePlatformAccounts() {
    const sessionId = 'your_session_id_here';
    
    const platformAccountsData = {
        "***********": {
            "phone": "***********",
            "username": "更新后的用户名1",
            "stats": {
                "followers": "1100",
                "total_reads": "55000",
                "total_income": "1600.00",
                "yesterday_reads": "220",
                "yesterday_income": "55.00",
                "credit_score": "96"
            },
            "account_status": "active"
        },
        "***********": {
            "phone": "***********",
            "username": "更新后的用户名2",
            "team_tag": "团队C",
            "stats": {
                "followers": "850",
                "total_reads": "32000",
                "credit_score": "90"
            }
        }
    };
    
    try {
        const result = await batchUpdatePlatformAccounts(sessionId, platformAccountsData);
        console.log('批量更新结果:', result);
    } catch (error) {
        console.error('批量更新失败:', error);
    }
}

// 执行批量更新
updateMultiplePlatformAccounts();
```

## Python示例

```python
import requests
import json

def batch_update_platform_accounts(session_id, platform_accounts_data):
    """批量更新平台账号信息"""
    url = "https://your-domain.com/api/user/batch-update-platform-accounts"
    data = {
        "sessionId": session_id,
        "user_id": 1,  # 用户ID
        "platformAccountsData": platform_accounts_data
    }
    
    response = requests.post(url, json=data)
    return response.json()

# 使用示例
if __name__ == "__main__":
    session_id = "your_session_id_here"
    
    platform_accounts_data = {
        "***********": {
            "phone": "***********",
            "username": "更新后的用户名1",
            "stats": {
                "followers": "1100",
                "total_reads": "55000",
                "total_income": "1600.00",
                "credit_score": "96"
            },
            "account_status": "active"
        },
        "***********": {
            "phone": "***********",
            "username": "更新后的用户名2",
            "team_tag": "团队C"
        }
    }
    
    try:
        result = batch_update_platform_accounts(session_id, platform_accounts_data)
        print("批量更新结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"批量更新失败: {e}")
```

## 平台账号数据结构

### 必填字段
- `phone`: 平台账号手机号
- `username`: 用户名
- `account_status`: 账号状态

### 可选字段
- `login_type`: 登录类型
- `team_tag`: 团队标签
- `data_update_time`: 数据更新时间
- `login_time`: 登录时间
- `sessionid`: 会话ID
- `homepage_url`: 主页URL
- `is_verified`: 是否认证
- `drafts_count`: 草稿数量
- `stats`: 统计数据对象
  - `followers`: 粉丝数
  - `total_reads`: 总阅读量
  - `total_income`: 总收入
  - `yesterday_reads`: 昨日阅读量
  - `yesterday_income`: 昨日收入
  - `credit_score`: 信用分
- `owner_id`: 所有者ID
- `current_holder_id`: 当前持有者ID
- `created_at`: 创建时间
- `updated_at`: 更新时间

## 权限说明

- 需要有效的用户会话
- 用户只能更新自己体系内的平台账号
- 主账号可以更新所有归属于自己的平台账号
- 子账号只能更新归属于自己的平台账号

## 注意事项

1. 手机号作为平台账号的唯一标识
2. 只更新提供的字段，未提供的字段保持不变
3. 系统会自动设置 `updated_at` 时间戳
4. 批量更新是原子性的，要么全部成功，要么全部失败
5. 建议单次批量更新的账号数量不超过100个

## 错误处理

### 常见错误响应

1. **会话无效**:
```json
{
  "success": false,
  "message": "会话无效"
}
```

2. **缺少数据**:
```json
{
  "success": false,
  "message": "缺少平台账号数据"
}
```

3. **权限不足**:
```json
{
  "success": false,
  "message": "没有权限操作此平台账号"
}
```

4. **数据格式错误**:
```json
{
  "success": false,
  "message": "数据格式错误或无效"
}
```
