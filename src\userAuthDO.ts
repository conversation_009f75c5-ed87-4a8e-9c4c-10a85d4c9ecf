import { DurableObject } from 'cloudflare:workers';
import type { Env, SSEConnection, UserSession, SessionManager } from './types';

// 用户认证Durable Object
export class UserAuthDO extends DurableObject<Env> {
  private sessions: SessionManager;
  private sseConnections: Map<string, SSEConnection>; // SSE连接管理
  private adminSSEConnections: Map<string, ReadableStreamDefaultController<Uint8Array>>; // 管理员SSE连接
  private initialized: boolean = false;

  constructor(ctx: DurableObjectState, env: Env) {
    super(ctx, env);

    this.sessions = {} as SessionManager;
    this.sessions.byPhone = new Map<string, number>();
    this.sessions.bySessionId = new Map<string, number>();
    this.sseConnections = new Map<string, SSEConnection>();
    this.adminSSEConnections = new Map<string, ReadableStreamDefaultController<Uint8Array>>();

    // 确保会话数据在任何操作前加载一次
    this.loadSessions();

    console.log('UserAuthDO 已初始化');
  }



  // 发送SSE消息
  private sendSSEMessage(controller: ReadableStreamDefaultController<Uint8Array>, data: any): void {
    try {
      const message = `data: ${JSON.stringify(data)}\n\n`;
      controller.enqueue(new TextEncoder().encode(message));
    } catch (error) {
      console.error('发送SSE消息失败:', error);
    }
  }

  // 验证管理员权限
  private async validateAdminAuth(request: Request): Promise<{ valid: boolean; message?: string }> {
    try {
      // 从请求头获取管理员认证信息
      const authHeader = request.headers.get('Authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return { valid: false, message: "缺少管理员认证信息" };
      }

      const token = authHeader.substring(7); // 移除 "Bearer " 前缀

      // 验证管理员token（这里可以是简单的密码验证或JWT验证）
      const storedPassword = await this.env.ADMIN_KV.get('admin_password');
      const storedUsername = await this.env.ADMIN_KV.get('admin_username');

      if (!storedPassword || !storedUsername) {
        return { valid: false, message: "管理员账号未初始化" };
      }

      // 简单验证：token应该是 username:password 的base64编码
      try {
        const decoded = atob(token);
        const [username, password] = decoded.split(':');

        if (username !== storedUsername || password !== storedPassword) {
          return { valid: false, message: "管理员认证失败" };
        }

        return { valid: true };
      } catch (error) {
        return { valid: false, message: "无效的认证格式" };
      }
    } catch (error) {
      return { valid: false, message: `管理员权限验证失败: ${error}` };
    }
  }



  private async loadSessions(): Promise<void> {
    if (this.initialized) return;
    try {
      const storedSessionsData = await this.ctx.storage.get<string>('sessions');
      const storedPhoneMapData = await this.ctx.storage.get<string>('phoneMap');
      const storedSessionIdMapData = await this.ctx.storage.get<string>('sessionIdMap');

      if (storedSessionsData) {
        const parsedSessions = JSON.parse(storedSessionsData);
        for (const userIdKey in parsedSessions) {
          const sessionData = parsedSessions[userIdKey];
          const numericUserId = parseInt(userIdKey, 10);
          if (!isNaN(numericUserId)) {
            this.sessions[numericUserId] = {
              ...sessionData,
              clientIds: new Set(sessionData.clientIds || [])
            };
          }
        }
        console.log(`已从存储中恢复会话数据: ${Object.keys(this.sessions).filter(k => k !== 'byPhone' && k !== 'bySessionId').length} 个用户会话`);
      }

      if (storedPhoneMapData) {
        this.sessions.byPhone = new Map(JSON.parse(storedPhoneMapData));
      } else {
        this.sessions.byPhone = new Map<string, number>();
      }

      if (storedSessionIdMapData) {
        this.sessions.bySessionId = new Map(JSON.parse(storedSessionIdMapData));
      } else {
        this.sessions.bySessionId = new Map<string, number>();
      }

    } catch (error) {
      console.error('从存储中加载会话数据失败:', error);
      this.sessions = {} as SessionManager;
      this.sessions.byPhone = new Map<string, number>();
      this.sessions.bySessionId = new Map<string, number>();
    }
    this.initialized = true;
  }

  private async saveSessions(): Promise<void> {
    if (!this.initialized) {
        await this.loadSessions();
    }
    try {
      const sessionsToStore: Record<string, any> = {};
      for (const userIdKey in this.sessions) {
        if (userIdKey === 'byPhone' || userIdKey === 'bySessionId') continue;
        const numericUserId = parseInt(userIdKey, 10);
        if (isNaN(numericUserId) || !this.sessions[numericUserId]) continue;
        const session = this.sessions[numericUserId];
        sessionsToStore[userIdKey] = {
          ...session,
          clientIds: Array.from(session.clientIds || [])
        };
      }
      const phoneEntries = Array.from(this.sessions.byPhone.entries());
      const sessionIdEntries = Array.from(this.sessions.bySessionId.entries());
      await this.ctx.storage.put('sessions', JSON.stringify(sessionsToStore));
      await this.ctx.storage.put('phoneMap', JSON.stringify(phoneEntries));
      await this.ctx.storage.put('sessionIdMap', JSON.stringify(sessionIdEntries));
      console.log('会话数据已保存到存储');
    } catch (error) {
      console.error('保存会话数据失败:', error);
    }
  }

  async fetch(request: Request): Promise<Response> {
    if (!this.initialized) { // 确保在处理任何请求前会话数据已加载
        await this.loadSessions();
    }

    const url = new URL(request.url);
    const path = url.pathname;
    console.log(`UserAuthDO 收到请求: ${request.method} ${path}`);

    // 先检查管理员端点（包括SSE）
    if (path === '/admin/sse') {
        console.log('收到管理员SSE连接请求');

        // 验证管理员SSE权限
        const url = new URL(request.url);
        const token = url.searchParams.get('token');

        if (!token) {
            console.log('管理员SSE连接缺少token');
            return new Response('Missing admin token', { status: 401 });
        }

        // 验证token
        const storedToken = await this.env.ADMIN_KV.get('admin_token');
        if (!storedToken || storedToken !== token) {
            console.log('管理员SSE token无效');
            return new Response('Invalid admin token', { status: 403 });
        }

        console.log('管理员SSE token验证成功');
        return this.handleAdminSSERequest(request);
    }

    // 处理批量进度推送
    if (path === '/admin/push-batch-progress') {
        if (request.method === 'POST') {
            return this.handlePushBatchProgress(request);
        }
        return new Response('Method Not Allowed for /admin/push-batch-progress', { status: 405 });
    }

    // 已移除 /admin/reset-alarm 路由，因为不再使用alarm机制
    if (path === '/admin/get-all-online-status') {
        // 为了保持向后兼容，暂时不验证管理员权限
        // TODO: 在前端实现完整的认证机制后，再启用严格验证

        // 批量获取所有用户在线状态
        if (request.method === 'POST') {
            return this.handleGetAllOnlineStatus(request);
        }
        return new Response('Method Not Allowed for /admin/get-all-online-status', { status: 405 });
    }

    // 检查用户SSE连接
    if (path === '/sse' || path === '/auth/sse') {
      return this.handleSSERequest(request);
    }

    switch (path) {
      case '/login':
        return this.handleLogin(request);
      case '/logout':
        return this.handleLogout(request);
      case '/check-session':
        return this.handleCheckSession(request);
      case '/check-user-online':
        return this.handleCheckUserOnline(request);
      case '/force-logout':
        return this.handleForceLogout(request);
      case '/notify-admin':
        return this.handleNotifyAdmin(request);
      case '/notify-user':
        return this.handleNotifyUser(request);
      case '/push-platform-data':
        return this.handlePushPlatformData(request);

      default:
        return new Response('Not Found in DO', { status: 404 });
    }
  }






  private async handleCheckUserOnline(request: Request): Promise<Response> {
    if (request.method !== 'POST') {
      return new Response('方法不允许', { status: 405 });
    }
    try {
      const { userId, phone } = await request.json<{ userId?: number; phone?: string }>();
      console.log(`检查用户在线状态: userId=${userId}, phone=${phone}`);

      let targetUserId: number | undefined = userId;
      if (!targetUserId && phone) {
        targetUserId = this.sessions.byPhone.get(phone);
      } else if (!targetUserId && !phone) {
        return new Response(JSON.stringify({ success: false, message: 'Either userId or phone must be provided.' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } });
      }

      let isOnline = false;
      let clientCount = 0;

      if (targetUserId !== undefined && this.sessions[targetUserId]) {
        const userSession = this.sessions[targetUserId];
        clientCount = userSession.clientIds.size;

        if (clientCount > 0) {
          // 用户在内存中有会话记录，需要通过SSE连接验证真实在线状态
          console.log(`用户 ${targetUserId} 在内存中有 ${clientCount} 个客户端连接，开始SSE验证`);
          isOnline = await this.verifyUserOnlineBySSE(targetUserId);

          if (!isOnline) {
            // 如果SSE验证失败，清理该用户的会话
            console.log(`用户 ${targetUserId} SSE验证失败，清理会话`);
            await this.clearUserSession(targetUserId, true);
            clientCount = 0;
          }
        }
      }

      console.log(`用户在线状态: ${isOnline ? '在线' : '离线'}, existingUserId=${targetUserId}, 客户端数量=${clientCount}`);
      return new Response(JSON.stringify({ success: true, online: isOnline, userId: targetUserId }),
        { headers: { 'Content-Type': 'application/json' } });
    } catch (error) {
      console.error('检查用户在线状态失败:', error);
      return new Response(JSON.stringify({ success: false, message: '检查用户在线状态失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 通过SSE连接验证用户真实在线状态
  private async verifyUserOnlineBySSE(userId: number): Promise<boolean> {
    try {
      console.log(`开始验证用户 ${userId} 的SSE在线状态`);

      // 查找用户的SSE连接
      let hasConnection = false;
      for (const [, connection] of this.sseConnections) {
        if (connection.userId === userId) {
          hasConnection = true;
          break;
        }
      }

      console.log(`用户 ${userId} SSE验证结果: ${hasConnection ? '在线' : '离线'}`);
      return hasConnection;

    } catch (error) {
      console.error(`SSE验证用户 ${userId} 失败:`, error);
      return false;
    }
  }



  // 批量获取所有用户在线状态（使用SSE连接验证）
  private async handleGetAllOnlineStatus(request: Request): Promise<Response> {
    try {
      const { userIds } = await request.json<{ userIds: number[] }>();
      console.log(`批量检查用户在线状态: userIds=${userIds?.join(',')}`);

      const onlineStatus: { [userId: number]: boolean } = {};

      if (userIds && Array.isArray(userIds)) {
        // 使用Promise.all并发验证所有用户的在线状态
        const verificationPromises = userIds.map(async (userId) => {
          if (this.sessions[userId] && this.sessions[userId].clientIds.size > 0) {
            // 有会话记录且有客户端连接，进行SSE连接验证
            const isOnline = await this.verifyUserOnlineBySSE(userId);
            if (!isOnline) {
              // SSE验证失败，清理会话
              console.log(`批量检查：用户 ${userId} SSE验证失败，清理会话`);
              await this.clearUserSession(userId, false); // 延迟保存，最后统一保存
            }
            return { userId, isOnline };
          } else {
            // 没有会话记录或没有客户端连接，直接返回离线
            return { userId, isOnline: false };
          }
        });

        const results = await Promise.all(verificationPromises);

        // 收集结果
        for (const result of results) {
          onlineStatus[result.userId] = result.isOnline;
        }

        // 统一保存会话数据（如果有清理操作）
        await this.saveSessions();
      }

      console.log(`批量在线状态检查完成，结果:`, onlineStatus);
      return new Response(JSON.stringify({ success: true, onlineStatus }),
        { headers: { 'Content-Type': 'application/json' } });
    } catch (error) {
      console.error('批量检查用户在线状态失败:', error);
      return new Response(JSON.stringify({ success: false, message: '批量检查用户在线状态失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 处理批量进度推送
  private async handlePushBatchProgress(request: Request): Promise<Response> {
    try {
      const progressMessage = await request.json();
      console.log('收到批量进度推送请求:', progressMessage);

      // 向所有管理员SSE连接推送进度
      let successfulPushes = 0;
      let failedPushes = 0;
      const totalAdminConnections = this.adminSSEConnections.size;

      if (totalAdminConnections === 0) {
        console.log('没有管理员SSE连接，跳过批量进度推送');
        return new Response(JSON.stringify({ success: true, message: '没有管理员在线，进度推送已忽略' }),
          { headers: { 'Content-Type': 'application/json' } });
      }

      const expiredConnections: string[] = [];

      for (const [connectionId, controller] of this.adminSSEConnections) {
        try {
          this.sendSSEMessage(controller, progressMessage);
          successfulPushes++;
          console.log(`成功向管理员推送批量进度: ${connectionId}`);
        } catch (error) {
          console.error(`向管理员推送批量进度失败: ${connectionId}`, error);
          failedPushes++;
          expiredConnections.push(connectionId);
        }
      }

      // 清理失效的连接
      for (const connectionId of expiredConnections) {
        this.adminSSEConnections.delete(connectionId);
      }

      return new Response(JSON.stringify({
        success: true,
        message: `批量进度已推送给 ${successfulPushes} 个管理员连接`,
        details: { successfulPushes, failedPushes, totalAdminConnections }
      }), { headers: { 'Content-Type': 'application/json' } });

    } catch (error) {
      console.error('处理批量进度推送失败:', error);
      return new Response(JSON.stringify({
        success: false,
        message: `批量进度推送失败: ${error}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  // 处理管理员SSE连接
  private handleAdminSSERequest(request: Request): Response {
    console.log('管理员SSE连接请求');

    // 创建SSE流
    const stream = new ReadableStream({
      start: (controller) => {
        // 保存管理员连接
        const connectionId = crypto.randomUUID();
        this.adminSSEConnections.set(connectionId, controller);

        console.log(`管理员SSE连接已建立: ${connectionId}`);

        // 发送连接成功消息
        this.sendSSEMessage(controller, {
          type: 'connected',
          message: '管理员SSE连接已建立',
          timestamp: new Date().toISOString()
        });
      },

      cancel: () => {
        console.log('管理员SSE连接已断开');
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    });
  }



  // 向管理员推送用户状态变化（使用SSE）
  private async notifyAdminStatusChange(userId: number, phone: string, isOnline: boolean): Promise<void> {
    console.log(`准备推送用户状态变化: userId=${userId}, phone=${phone}, isOnline=${isOnline}`);

    const statusMessage = {
      type: 'user_status_change',
      data: {
        userId,
        phone,
        isOnline,
        timestamp: new Date().toISOString()
      }
    };

    let successfulPushes = 0;
    let failedPushes = 0;
    const totalAdminConnections = this.adminSSEConnections.size;

    if (totalAdminConnections === 0) {
      console.log('没有管理员SSE连接，跳过状态推送');
      return;
    }

    // 向所有管理员SSE连接推送状态变化
    const expiredConnections: string[] = [];

    for (const [connectionId, controller] of this.adminSSEConnections) {
      try {
        this.sendSSEMessage(controller, statusMessage);
        successfulPushes++;
        console.log(`成功向管理员SSE推送状态变化: ${connectionId}`);
      } catch (error) {
        console.error(`向管理员SSE推送失败: ${connectionId}`, error);
        failedPushes++;
        expiredConnections.push(connectionId);
      }
    }

    // 清理失效的连接
    for (const connectionId of expiredConnections) {
      this.adminSSEConnections.delete(connectionId);
    }

    console.log(`管理员状态推送完成: 总管理员连接数=${totalAdminConnections}, 成功推送=${successfulPushes}, 失败=${failedPushes}, userId=${userId}, phone=${phone}, isOnline=${isOnline}`);
  }

  // 处理用户SSE连接
  private handleSSERequest(request: Request): Response {
    const url = new URL(request.url);
    const sessionId = url.searchParams.get('sessionId');
    const clientId = url.searchParams.get('clientId') || crypto.randomUUID();

    console.log(`SSE连接请求: sessionId=${sessionId}, clientId=${clientId}`);

    if (!sessionId) {
      return new Response('需要会话ID', { status: 400 });
    }

    const userId = this.sessions.bySessionId.get(sessionId);
    if (!userId || !this.sessions[userId]) {
      console.log(`无效的会话ID: ${sessionId}`);
      return new Response('会话无效或已过期', { status: 401 });
    }

    // 创建SSE流
    const stream = new ReadableStream({
      start: (controller) => {
        const connectionId = `${userId}_${clientId}`;
        const now = Date.now();

        // 保存SSE连接
        const sseConnection: SSEConnection = {
          controller,
          userId,
          clientId,
          sessionId,
          connectedAt: now
        };

        this.sseConnections.set(connectionId, sseConnection);

        const wasOffline = this.sessions[userId].clientIds.size === 0;

        // 更新用户会话
        this.sessions[userId].clientIds.add(clientId);
        this.sessions[userId].lastActiveTime = now;

        this.saveSessions();

        // 如果用户之前是离线状态，现在上线了，推送状态变化
        if (wasOffline) {
          const userPhone = this.sessions[userId].phone;
          console.log(`用户 ${userId} (${userPhone}) 从离线变为在线，推送状态变化给管理员`);
          this.notifyAdminStatusChange(userId, userPhone, true);
        }

        console.log(`SSE连接已建立: userId=${userId}, clientId=${clientId}`);

        // 发送连接成功消息
        this.sendSSEMessage(controller, {
          type: 'connected',
          message: 'SSE连接已建立',
          timestamp: new Date().toISOString()
        });
      },

      cancel: () => {
        const connectionId = `${userId}_${clientId}`;
        console.log(`SSE连接已断开: userId=${userId}, clientId=${clientId}`);

        // 清理连接
        this.sseConnections.delete(connectionId);

        if (this.sessions[userId]) {
          this.sessions[userId].clientIds.delete(clientId);

          // 如果用户没有剩余连接，推送下线状态
          if (this.sessions[userId].clientIds.size === 0) {
            const userPhone = this.sessions[userId].phone;
            this.notifyAdminStatusChange(userId, userPhone, false);
          }

          this.saveSessions();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    });
  }







  private async handleLogin(request: Request): Promise<Response> {
    if (request.method !== 'POST') { return new Response('方法不允许', { status: 405 }); }
    try {
      const { userId, phone, forceLogin = false, completeUserInfo } = await request.json<{
        userId: number;
        phone: string;
        forceLogin?: boolean;
        completeUserInfo?: any;
      }>();
      if (!userId || !phone) {
        return new Response(JSON.stringify({ success: false, message: 'User ID 和 phone 是必需的。' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } });
      }
      console.log(`处理登录请求: userId=${userId}, phone=${phone}, forceLogin=${forceLogin}`);
      const existingUserIdByPhone = this.sessions.byPhone.get(phone);
      if (existingUserIdByPhone !== undefined && this.sessions[existingUserIdByPhone]) {
        console.log(`内存中发现手机号 ${phone} 的会话记录，userId=${existingUserIdByPhone}，开始验证真实在线状态`);

        // 使用SSE连接验证真实在线状态
        const isReallyOnline = await this.verifyUserOnlineBySSE(existingUserIdByPhone);

        if (!isReallyOnline) {
          console.log(`用户 ${existingUserIdByPhone} SSE验证失败，清理过期会话，允许新登录`);
          await this.clearUserSession(existingUserIdByPhone, true);
          // 会话已清理，继续正常登录流程
        } else {
          console.log(`用户 ${existingUserIdByPhone} SSE验证成功，确实在线`);
          if (existingUserIdByPhone === userId) { // 同一个用户在别处登录
              if (forceLogin) {
                  console.log(`对同一用户 (userId ${userId}) 执行强制登录。通知现有客户端。`);
                  await this.notifyClientsForcedLogout(existingUserIdByPhone, '您的账号已在另一处登录 (强制)');
                  await this.clearUserSession(existingUserIdByPhone, false); // 清理但不立即保存
              } else {
                  console.log(`用户 ${userId} 已登录且在线。未请求强制登录。`);
                  return new Response(JSON.stringify({ success: false, message: '账号已在其他设备登录', requireForceLogin: true }),
                      { headers: { 'Content-Type': 'application/json' } });
              }
          } else { // 不同用户尝试用同一个手机号登录
              if (forceLogin) {
                  await this.notifyClientsForcedLogout(existingUserIdByPhone, '您的手机号已被用于新的登录会话');
                  await this.clearUserSession(existingUserIdByPhone, false);
              } else {
                  return new Response(JSON.stringify({ success: false, message: '手机号已被其他账号占用', requireForceLogin: true }),
                      { headers: { 'Content-Type': 'application/json' } });
              }
          }
        }
      }
      const newSessionId = crypto.randomUUID();
      this.sessions[userId] = {
        userId,
        phone,
        sessionId: newSessionId,
        lastActiveTime: Date.now(),
        clientIds: new Set<string>(),
        lastVerifyTime: Date.now(), // 初始化验证时间
      };
      this.sessions.byPhone.set(phone, userId);
      this.sessions.bySessionId.set(newSessionId, userId);
      await this.saveSessions();

      console.log(`为 userId=${userId}, phone=${phone}, sessionId=${newSessionId} 创建新会话成功。`);

      // 构建返回的响应，包含完整用户信息
      const loginResponse: any = {
        success: true,
        message: '登录成功',
        sessionId: newSessionId
      };

      // 如果有完整用户信息，添加到响应中
      if (completeUserInfo) {
        loginResponse.user = completeUserInfo;
      }

      return new Response(JSON.stringify(loginResponse),
        { headers: { 'Content-Type': 'application/json' } });
    } catch (error) {
      console.error('登录失败:', error);
      return new Response(JSON.stringify({ success: false, message: '登录处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  private async handleLogout(request: Request): Promise<Response> {
    if (request.method !== 'POST') { return new Response('方法不允许', { status: 405 }); }
    try {
      const { sessionId } = await request.json<{ sessionId: string }>();
      if (!sessionId) {
        return new Response(JSON.stringify({ success: false, message: 'Session ID 是必需的。' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } });
      }
      console.log(`处理登出请求: sessionId=${sessionId}`);
      const userId = this.sessions.bySessionId.get(sessionId);
      if (!userId || !this.sessions[userId]) {
        console.log(`登出: 未找到 sessionId=${sessionId} 的会话。`);
        return new Response(JSON.stringify({ success: false, message: '会话不存在或已失效' }),
          { headers: { 'Content-Type': 'application/json' } });
      }

      // 获取用户信息用于推送状态变化
      const userSession = this.sessions[userId];
      const userPhone = userSession.phone;

      await this.notifyClientsForcedLogout(userId, '您已成功退出登录');

      // 推送用户下线状态给管理员
      await this.notifyAdminStatusChange(userId, userPhone, false);

      await this.clearUserSession(userId, true, false); // 保存但不重复推送给管理员

      console.log(`用户 ${userId} 登出成功。`);
      return new Response(JSON.stringify({ success: true, message: '登出成功', userId }),
        { headers: { 'Content-Type': 'application/json' } });
    } catch (error) {
      console.error('登出失败:', error);
      return new Response(JSON.stringify({ success: false, message: '登出处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  private async handleCheckSession(request: Request): Promise<Response> {
    if (request.method !== 'POST') { return new Response('方法不允许', { status: 405 }); }
    try {
      const { sessionId } = await request.json<{ sessionId: string }>();
      if (!sessionId) {
         return new Response(JSON.stringify({ valid: false, message: 'Session ID 是必需的。' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } });
      }
      console.log(`检查会话: sessionId=${sessionId}`);
      const userId = this.sessions.bySessionId.get(sessionId);
      const userSession = userId ? this.sessions[userId] : undefined;
      if (!userSession) {
        console.log(`检查会话: 未找到或无效的 sessionId=${sessionId}`);
        return new Response(JSON.stringify({ valid: false, message: '会话无效或已过期' }),
          { headers: { 'Content-Type': 'application/json' } });
      }
      userSession.lastActiveTime = Date.now();
      if (Math.random() < 0.2) { // 20% 概率保存
          await this.saveSessions();
      }
      return new Response(JSON.stringify({
        valid: true,
        userId: userSession.userId,
        phone: userSession.phone,
        clientCount: userSession.clientIds.size
      }), { headers: { 'Content-Type': 'application/json' } });
    } catch (error) {
      console.error('检查会话失败:', error);
      return new Response(JSON.stringify({ valid: false, message: '检查会话请求处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  private async notifyClientsForcedLogout(userId: number, reason: string): Promise<void> {
    const session = this.sessions[userId];
    console.log(`通知客户端强制登出: userId=${userId}, reason='${reason}', 内存中会话的 clientIds 数量 (如果存在): ${session ? session.clientIds.size : 'N/A'}`);

    let notifiedCount = 0;
    const expiredConnections: string[] = [];

    // 向用户的所有SSE连接发送强制登出消息
    for (const [connectionId, connection] of this.sseConnections) {
      if (connection.userId === userId) {
        try {
          console.log(`通知强制登出: 发送给 userId=${userId}, clientId=${connection.clientId}`);

          // 发送强制登出消息
          this.sendSSEMessage(connection.controller, {
            type: 'force_logout',
            message: reason,
            timestamp: new Date().toISOString()
          });

          notifiedCount++;

          // 标记连接为过期，稍后清理
          expiredConnections.push(connectionId);

          // 从内存会话中清理
          if (session) {
            session.clientIds.delete(connection.clientId);
          }
        } catch (error) {
          console.error(`通知强制登出: 处理 userId=${userId} 的 SSE连接时出错:`, error);
          expiredConnections.push(connectionId);
        }
      }
    }

    // 清理被强制登出的连接
    for (const connectionId of expiredConnections) {
      this.sseConnections.delete(connectionId);
    }



    // 如果 session.clientIds 被修改了，需要保存
    if (session && notifiedCount > 0) {
      await this.saveSessions();
    }
  }

  private async clearUserSession(userId: number, shouldSave: boolean = true, notifyAdmin: boolean = true): Promise<void> {
    const session = this.sessions[userId];
    if (!session) {
      return;
    }

    const userPhone = session.phone;

    this.sessions.byPhone.delete(session.phone);
    this.sessions.bySessionId.delete(session.sessionId);
    delete this.sessions[userId]; // 从主对象中删除

    // 推送用户下线状态给管理员（如果需要）
    if (notifyAdmin) {
      await this.notifyAdminStatusChange(userId, userPhone, false);
    }

    // 持久化存储中的 'sessions', 'phoneMap', 'sessionIdMap' 会在下次 saveSessions 时被更新的状态覆盖
    // 如果希望立即删除这些key（如果这是最后一个用户被清除），需要更复杂的逻辑
    // 通常，让 saveSessions() 用空内容（或减少后的内容）覆盖是可以接受的
    if (shouldSave) {
        await this.saveSessions();
        console.log(`已清除 userId=${userId} 的用户会话并保存了更新后的会话数据。`);
    } else {
        console.log(`已从内存中清除 userId=${userId} 的用户会话。保存已推迟。`);
    }
  }

  private async handleForceLogout(request: Request): Promise<Response> {
    if (request.method !== 'POST') { return new Response('方法不允许', { status: 405 }); }
    try {
      const { userId, phone } = await request.json<{ userId?: number; phone?: string }>();
      console.log(`处理强制下线请求: userId=${userId}, phone=${phone}`);
      let targetUserId: number | undefined = userId;
      if (!targetUserId && phone) {
        targetUserId = this.sessions.byPhone.get(phone);
      }
      if (!targetUserId || !this.sessions[targetUserId]) { // 确保目标用户存在于会话中
        console.log(`强制下线: 未找到用户或用户不在线。提供的 userId=${userId}, phone=${phone}. 解析后的 targetUserId=${targetUserId}`);
        return new Response(JSON.stringify({ success: false, message: '用户不在线或未找到' }),
          { headers: { 'Content-Type': 'application/json' } });
      }
      console.log(`正在为 userId=${targetUserId} 执行强制下线`);

      // 获取用户信息用于推送状态变化
      const userSession = this.sessions[targetUserId];
      const userPhone = userSession.phone;

      await this.notifyClientsForcedLogout(targetUserId, '您已被管理员强制下线');

      // 推送用户下线状态给管理员
      await this.notifyAdminStatusChange(targetUserId, userPhone, false);

      await this.clearUserSession(targetUserId, true, false); // 清理并保存，但不重复推送给管理员
      return new Response(JSON.stringify({ success: true, message: '用户已被强制下线', userId: targetUserId }),
        { headers: { 'Content-Type': 'application/json' } });
    } catch (error) {
      console.error('强制下线处理失败:', error);
      return new Response(JSON.stringify({ success: false, message: '强制下线处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 处理向管理员推送通知的请求
  private async handleNotifyAdmin(request: Request): Promise<Response> {
    try {
      if (request.method !== 'POST') {
        return new Response('Method Not Allowed', { status: 405 });
      }

      const { type, message, data } = await request.json<{
        type: string;
        message: string;
        data?: any;
      }>();

      if (!type || !message) {
        return new Response(JSON.stringify({ success: false, message: '缺少必要参数' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } });
      }

      // 向所有管理员SSE连接推送通知
      const notificationMessage = {
        type: type,
        message: message,
        data: data,
        timestamp: new Date().toISOString()
      };

      let successfulPushes = 0;
      let failedPushes = 0;
      const totalAdminConnections = this.adminSSEConnections.size;

      if (totalAdminConnections === 0) {
        console.log('没有管理员SSE连接，跳过通知推送');
        return new Response(JSON.stringify({ success: true, message: '没有管理员在线，通知已忽略' }),
          { headers: { 'Content-Type': 'application/json' } });
      }

      // 向所有管理员SSE连接推送通知
      const expiredConnections: string[] = [];

      for (const [connectionId, controller] of this.adminSSEConnections) {
        try {
          this.sendSSEMessage(controller, notificationMessage);
          successfulPushes++;
          console.log(`成功向管理员推送通知: ${connectionId}`);
        } catch (error) {
          console.error(`向管理员推送通知失败: ${connectionId}`, error);
          failedPushes++;
          expiredConnections.push(connectionId);
        }
      }

      // 清理失效的连接
      for (const connectionId of expiredConnections) {
        this.adminSSEConnections.delete(connectionId);
      }



      return new Response(JSON.stringify({
        success: true,
        message: `通知已推送给 ${successfulPushes} 个管理员连接`,
        details: { successfulPushes, failedPushes, totalAdminConnections }
      }), { headers: { 'Content-Type': 'application/json' } });

    } catch (error) {
      console.error('处理管理员通知推送失败:', error);
      return new Response(JSON.stringify({ success: false, message: '推送处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 处理向特定用户推送通知的请求
  private async handleNotifyUser(request: Request): Promise<Response> {
    try {
      if (request.method !== 'POST') {
        return new Response('Method Not Allowed', { status: 405 });
      }

      const requestBody = await request.json<{
        targetUserId?: number;
        userId?: number;
        type?: string;
        message?: string;
        data?: any;
        notification?: {
          type: string;
          message: string;
          data?: any;
        };
      }>();

      // 支持两种格式：
      // 1. { targetUserId, type, message, data }
      // 2. { userId, notification: { type, message, data } }
      let targetUserId: number;
      let type: string;
      let message: string;
      let data: any;

      if (requestBody.notification) {
        // 新格式
        targetUserId = requestBody.userId!;
        type = requestBody.notification.type;
        message = requestBody.notification.message;
        data = requestBody.notification.data;
      } else {
        // 旧格式
        targetUserId = requestBody.targetUserId!;
        type = requestBody.type!;
        message = requestBody.message!;
        data = requestBody.data;
      }

      if (!targetUserId || !type || !message) {
        console.log('推送参数检查失败:', { targetUserId, type, message, requestBody });
        return new Response(JSON.stringify({ success: false, message: '缺少必要参数' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } });
      }

      // 检查目标用户是否在线
      const userSession = this.sessions[targetUserId];
      if (!userSession || userSession.clientIds.size === 0) {
        console.log(`用户 ${targetUserId} 不在线，跳过通知推送`);
        return new Response(JSON.stringify({ success: true, message: '用户不在线，通知已忽略' }),
          { headers: { 'Content-Type': 'application/json' } });
      }

      // 准备推送消息
      const notificationMessage = {
        type: type,
        message: message,
        data: data,
        timestamp: new Date().toISOString()
      };

      let successfulPushes = 0;
      let failedPushes = 0;
      const expiredConnections: string[] = [];

      // 向用户的所有SSE连接推送通知
      for (const [connectionId, connection] of this.sseConnections) {
        if (connection.userId === targetUserId) {
          try {
            this.sendSSEMessage(connection.controller, notificationMessage);
            successfulPushes++;
            console.log(`成功向用户 ${targetUserId} 推送通知: ${connectionId}`);
          } catch (error) {
            console.error(`向用户 ${targetUserId} 推送通知失败: ${connectionId}`, error);
            failedPushes++;
            expiredConnections.push(connectionId);
          }
        }
      }

      // 清理失效的连接
      for (const connectionId of expiredConnections) {
        this.sseConnections.delete(connectionId);
        // 同时更新用户会话中的客户端ID
        if (userSession) {
          const clientId = connectionId.split('_')[1];
          userSession.clientIds.delete(clientId);
        }
      }

      if (expiredConnections.length > 0) {
        this.saveSessions();
      }

      console.log(`用户通知推送完成: userId=${targetUserId}, 成功=${successfulPushes}, 失败=${failedPushes}`);

      return new Response(JSON.stringify({
        success: true,
        message: `通知已推送给用户 ${targetUserId} 的 ${successfulPushes} 个连接`,
        details: { targetUserId, successfulPushes, failedPushes }
      }), { headers: { 'Content-Type': 'application/json' } });

    } catch (error) {
      console.error('处理用户通知推送失败:', error);
      return new Response(JSON.stringify({ success: false, message: '推送处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 处理平台数据推送请求
  private async handlePushPlatformData(request: Request): Promise<Response> {
    try {
      if (request.method !== 'POST') {
        return new Response('Method Not Allowed', { status: 405 });
      }

      const requestBody = await request.json<{
        type: string;
        target: 'admin' | 'user';
        mainAccountId?: number;
        data: any;
      }>();

      const { type, target, mainAccountId, data } = requestBody;

      if (!type || !target || !data) {
        return new Response(JSON.stringify({ success: false, message: '缺少必要参数' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } });
      }

      if (target === 'admin') {
        // 推送给所有管理员
        return await this.pushToAdmins(type, data);
      } else if (target === 'user' && mainAccountId) {
        // 推送给特定主账号及其子账号
        return await this.pushToMainAccountUsers(mainAccountId, type, data);
      } else {
        return new Response(JSON.stringify({ success: false, message: '无效的推送目标' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } });
      }

    } catch (error) {
      console.error('处理平台数据推送失败:', error);
      return new Response(JSON.stringify({ success: false, message: '推送处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 推送给所有管理员
  private async pushToAdmins(type: string, data: any): Promise<Response> {
    try {
      console.log('开始推送平台数据给所有管理员...');

      const pushMessage = {
        type: type,
        data: data,
        timestamp: new Date().toISOString()
      };

      let successfulPushes = 0;
      let failedPushes = 0;
      const totalAdminConnections = this.adminSSEConnections.size;

      if (totalAdminConnections === 0) {
        console.log('没有管理员在线，跳过推送');
        return new Response(JSON.stringify({ success: true, message: '没有管理员在线，推送已忽略' }),
          { headers: { 'Content-Type': 'application/json' } });
      }

      // 向所有管理员SSE连接推送平台数据
      const expiredConnections: string[] = [];

      for (const [connectionId, controller] of this.adminSSEConnections) {
        try {
          this.sendSSEMessage(controller, pushMessage);
          successfulPushes++;
          console.log(`成功向管理员推送平台数据: ${connectionId}`);
        } catch (error) {
          console.error(`向管理员推送平台数据失败: ${connectionId}`, error);
          failedPushes++;
          expiredConnections.push(connectionId);
        }
      }

      // 清理失效的连接
      for (const connectionId of expiredConnections) {
        this.adminSSEConnections.delete(connectionId);
      }

      return new Response(JSON.stringify({
        success: true,
        message: `平台数据已推送给 ${successfulPushes} 个管理员连接`,
        details: { successfulPushes, failedPushes, totalAdminConnections }
      }), { headers: { 'Content-Type': 'application/json' } });

    } catch (error) {
      console.error('推送平台数据给管理员失败:', error);
      return new Response(JSON.stringify({ success: false, message: '推送处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 推送给特定主账号及其子账号
  private async pushToMainAccountUsers(mainAccountId: number, type: string, data: any): Promise<Response> {
    try {
      console.log(`开始推送平台数据给主账号 ${mainAccountId} 及其子账号...`);

      const pushMessage = {
        type: type,
        data: data,
        timestamp: new Date().toISOString()
      };

      let successfulPushes = 0;
      let failedPushes = 0;
      const expiredConnections: string[] = [];

      // 查找主账号和子账号的用户ID
      const targetUserIds = await this.findUserIdsByMainAccount(mainAccountId);

      if (targetUserIds.length === 0) {
        console.log(`主账号 ${mainAccountId} 及其子账号都不在线`);
        return new Response(JSON.stringify({ success: true, message: '目标用户都不在线，推送已忽略' }),
          { headers: { 'Content-Type': 'application/json' } });
      }

      // 向目标用户的所有SSE连接推送平台数据
      for (const [connectionId, connection] of this.sseConnections) {
        if (targetUserIds.includes(connection.userId)) {
          try {
            this.sendSSEMessage(connection.controller, pushMessage);
            successfulPushes++;
            console.log(`成功向用户 ${connection.userId} 推送平台数据: ${connectionId}`);
          } catch (error) {
            console.error(`向用户 ${connection.userId} 推送平台数据失败: ${connectionId}`, error);
            failedPushes++;
            expiredConnections.push(connectionId);
          }
        }
      }

      // 清理失效的连接
      for (const connectionId of expiredConnections) {
        this.sseConnections.delete(connectionId);
      }

      return new Response(JSON.stringify({
        success: true,
        message: `平台数据已推送给主账号 ${mainAccountId} 的 ${successfulPushes} 个用户连接`,
        details: { mainAccountId, targetUserIds, successfulPushes, failedPushes }
      }), { headers: { 'Content-Type': 'application/json' } });

    } catch (error) {
      console.error(`推送平台数据给主账号 ${mainAccountId} 失败:`, error);
      return new Response(JSON.stringify({ success: false, message: '推送处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 根据主账号ID查找相关的用户ID（主账号和子账号）
  private async findUserIdsByMainAccount(mainAccountId: number): Promise<number[]> {
    try {
      const userIds: number[] = [];

      // 遍历所有在线用户，查找主账号ID匹配的用户
      for (const [userId, session] of Object.entries(this.sessions)) {
        if (typeof session === 'object' && session.clientIds && session.clientIds.size > 0) {
          // 这里需要根据实际的用户-主账号关系来判断
          // 暂时简化处理，实际应该查询数据库获取用户的主账号关系
          // 假设用户ID就是主账号ID，或者有某种关联关系
          const numericUserId = parseInt(userId);
          if (numericUserId === mainAccountId) {
            userIds.push(numericUserId);
          }
          // TODO: 添加查找子账号的逻辑
        }
      }

      console.log(`主账号 ${mainAccountId} 找到 ${userIds.length} 个在线用户:`, userIds);
      return userIds;

    } catch (error) {
      console.error(`查找主账号 ${mainAccountId} 相关用户失败:`, error);
      return [];
    }
  }
}