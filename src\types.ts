import type { DurableObjectNamespace, D1Database, KVNamespace } from '@cloudflare/workers-types';

// ==================== 环境配置接口 ====================

export interface Env {
  // 用户认证 Durable Object 绑定
  USER_AUTH_DO: DurableObjectNamespace;

  // 批量处理器 Durable Object 绑定
  BATCH_PROCESSOR_DO: DurableObjectNamespace;

  // D1 数据库绑定
  DB: D1Database;

  // 静态资源绑定
  ASSETS: { fetch: (request: Request) => Promise<Response> };

  // 管理 KV 绑定
  ADMIN_KV: KVNamespace;
}

// ==================== 用户相关接口 ====================

/**
 * 用户基础信息接口 - 统一定义
 */
export interface User {
  id: number;
  phone: string;
  password: string;
  created_at: string;
  last_login_at: string | null;
  expiry_date: string | null;
  account_type: string;
  account_owner: number | null;
}

// ==================== 激活码相关接口 ====================

/**
 * 激活码类型枚举
 */
export enum ActivationCodeType {
  DAY_1 = 1,
  DAY_7 = 7,
  DAY_30 = 30,
  DAY_90 = 90,
  DAY_365 = 365
}

/**
 * 激活码接口
 */
export interface ActivationCode {
  id: number;
  code: string;
  type: ActivationCodeType;
  created_at: string;
  used_at: string | null;
  used_by: number | null;
}

/**
 * 激活记录接口
 */
export interface ActivationRecord {
  id: number;
  code: string;
  user_id: number;
  used_at: string;
  phone: string;
  days: number;
}

// ==================== 平台账号相关接口 ====================

/**
 * 平台账号统计数据接口
 */
export interface PlatformAccountStats {
  followers: string;
  total_reads: string;
  total_income: string;
  yesterday_reads: string;
  yesterday_income: string;
  credit_score: string;
  can_withdraw_amount: string; // 可提现收益
}

/**
 * 平台账号完整数据接口 - 统一定义
 */
export interface PlatformAccountData {
  phone: string;
  platform: string; // 平台名称（如：今日头条、抖音等）
  login_type: string; // 内容类型：视频、文章、微头条
  team_tag: string;
  data_update_time: string;
  login_time: string;
  username: string;
  sessionid: string;
  homepage_url: string;
  is_verified: string;
  drafts_count: string;
  stats: PlatformAccountStats;
  account_status: string;
  punish_info?: string; // 禁言详细信息（可选）
  is_yesterday_income_ready: boolean; // 昨日收益是否已准备好
  owner_id: number; // 谁添加的这个平台账号
  current_holder_id: number; // 当前归属于哪个账号
  created_at: string;
  updated_at: string;
}

/**
 * 主账号平台数据接口（新架构）
 */
export interface MainAccountPlatformData {
  main_account_id: number;
  platform_accounts_data: string; // JSON格式存储该主账号体系的所有平台账号
  updated_at: string;
}

/**
 * 平台账号集合接口
 */
export interface PlatformAccountsCollection {
  accounts: { [phone: string]: PlatformAccountData };
  metadata: {
    total_accounts: number;
    last_batch_update: string;
  };
}

// ==================== 数据操作结果接口 ====================

/**
 * 统一的数据结果接口
 */
export interface FetchResult {
  success: boolean;
  phone: string;
  message: string;
  isYesterdayIncomeReady?: boolean;
  accountOffline?: boolean;
  data?: Partial<PlatformAccountData>;
}

/**
 * 单个操作结果接口
 */
export interface OperationResult {
  success: boolean;
  phone: string;
  message: string;
  data?: Partial<PlatformAccountData>;
  isYesterdayIncomeReady?: boolean;
  accountOffline?: boolean;
}

/**
 * 批量操作结果接口
 */
export interface BatchOperationResult {
  success: boolean;
  message: string;
  totalAccounts: number;
  successCount: number;
  failureCount: number;
  skippedCount: number;
  results: FetchResult[];
  // 智能批量处理的额外字段
  phase?: 'waiting' | 'processing' | 'completed' | 'failed';
  needsPush?: boolean;
  mainAccountGroups?: any;
}

// ==================== SSE连接相关接口 ====================

/**
 * SSE连接信息接口
 */
export interface SSEConnection {
  controller: ReadableStreamDefaultController<Uint8Array>;
  userId: number;
  clientId: string;
  sessionId: string;
  connectedAt: number;
}

/**
 * 用户会话信息接口
 */
export interface UserSession {
  userId: number;
  phone: string;
  sessionId: string;
  lastActiveTime: number;
  clientIds: Set<string>; // 存储客户端连接ID
  lastVerifyTime?: number; // 上次验证时间
}

/**
 * 会话管理器接口
 */
export interface SessionManager {
  [userId: number]: UserSession;
  byPhone: Map<string, number>; // 手机号到用户ID的映射
  bySessionId: Map<string, number>; // 会话ID到用户ID的映射
}

// ==================== 数据获取器相关接口 ====================

/**
 * 数据获取器类型 - 定义各种数据获取函数的统一接口
 */
export type DataFetcher = (phone: string, sessionid: string) => Promise<OperationResult>;

/**
 * 数据获取器配置
 */
export interface FetcherConfig {
  name: string;
  description: string;
  fetcher: DataFetcher;
}