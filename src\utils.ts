// 密码简单加密函数 (实际生产环境应使用更安全的方法)
export function encryptPassword(password: string): string {
  // 简化版加密，实际环境应该使用bcrypt或其他安全算法
  // 在Cloudflare Workers环境中，可以使用SubtleCrypto API
  
  // 使用更可靠的编码方式，确保一致性
  try {
    // 对非ASCII字符进行处理，避免btoa报错
    return btoa(unescape(encodeURIComponent(password)));
  } catch (error) {
    console.error("密码加密失败:", error);
    // 如果上面的方法也失败，返回一个简单的哈希
    let hash = 0;
    for (let i = 0; i < password.length; i++) {
      const char = password.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return hash.toString(36);
  }
}

// 根据路径解析API请求
export function parseUrl(url: URL): { 
  action: string;
  params: { [key: string]: string };
} {
  const pathParts = url.pathname.split('/').filter(Boolean);
  const action = pathParts[1] || '';
  
  // 解析查询参数
  const params: { [key: string]: string } = {};
  url.searchParams.forEach((value, key) => {
    params[key] = value;
  });
  
  return { action, params };
}

// 生成成功响应
export function jsonResponse(data: any, status = 200): Response {
  return new Response(JSON.stringify(data), {
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
    status,
  });
}

// 生成错误响应
export function errorResponse(message: string, status = 400): Response {
  return jsonResponse({ success: false, message }, status);
}

// 处理跨域预检请求
export function handleCors(): Response {
  return new Response(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400',
    },
  });
} 