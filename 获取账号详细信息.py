import requests
import json
import re

def extract_user_info(html_content):
    """
    从HTML内容中提取用户信息

    参数:
    html_content: HTML页面内容

    返回:
    dict: 包含id和screen_name的字典
    """
    user_info = {}

    try:
        # 找到"user"的位置
        user_pos = html_content.find('"user"')
        if user_pos == -1:
            print("未找到 'user' 字段")
            return user_info

        print(f"找到 'user' 字段，位置: {user_pos}")

        # 从整个HTML开始搜索has_already_authentication（因为它在user之前）
        auth_pattern = r'"has_already_authentication":\s*(true|false)'
        auth_match = re.search(auth_pattern, html_content)
        if auth_match:
            auth_value = auth_match.group(1) == 'true'
            user_info['has_already_authentication'] = auth_value
            auth_status = "已实名" if auth_value else "未实名"
            print(f"实名状态: {auth_status} ({auth_value})")

        # 从"user"位置开始搜索，找到第一个"id"
        search_start = user_pos
        id_pattern = r'"id":\s*(\d+)'
        id_match = re.search(id_pattern, html_content[search_start:])
        if id_match:
            user_info['id'] = id_match.group(1)
            print(f"找到ID: {user_info['id']}")

        # 从"user"位置开始搜索，找到第一个"screen_name"
        name_pattern = r'"screen_name":\s*"([^"]*)"'
        name_match = re.search(name_pattern, html_content[search_start:])
        if name_match:
            user_info['screen_name'] = name_match.group(1)
            print(f"找到昵称: {user_info['screen_name']}")

    except Exception as e:
        print(f"解析用户信息时出错: {e}")

    return user_info


def get_account_info(sessionid=None):
    """
    获取账号详细信息并提取用户ID和昵称

    参数:
    sessionid: sessionid值，需要从浏览器开发者工具中获取

    如何获取sessionid:
    1. 打开浏览器开发者工具 (F12)
    2. 访问头条创作者平台并登录
    3. 在Network标签页中找到相关请求
    4. 复制请求头中Cookie里的sessionid值
    """

    # 1. Define the URL from the GET request line
    # It's the host + the path/query parameters
    url = "https://mp.toutiao.com/profile_v4/index"

    # 2. Define the headers as a dictionary
    # Copy all headers from your raw request, especially the 'Cookie' and 'User-Agent'
    headers = {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Cache-Control": "max-age=0",
        "Priority": "u=0, i",
        "Sec-Ch-Ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": '"Windows"',
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "cross-site",
        "Upgrade-Insecure-Requests": "1",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    }

    # 添加Cookie（如果提供）
    if sessionid:
        headers["Cookie"] = f"sessionid={sessionid}"

    # 3. Send the GET request
    try:
        response = requests.get(url, headers=headers)

        # 4. Check the response and print the data
        # A status code of 200 means success!
        if response.status_code == 200:
            # 检查响应内容类型
            content_type = response.headers.get('content-type', '')
            if 'application/json' in content_type:
                # JSON响应
                data = response.json()
                print("✅ Request successful!")
                print(json.dumps(data, indent=2, ensure_ascii=False))

                # 从JSON中提取用户信息
                if 'user' in data:
                    user_data = data['user']
                    user_id = user_data.get('id', 'N/A')
                    screen_name = user_data.get('screen_name', 'N/A')
                    has_auth = user_data.get('has_already_authentication', None)
                    print(f"\n📋 用户信息:")
                    print(f"ID: {user_id}")
                    print(f"昵称: {screen_name}")
                    if has_auth is not None:
                        auth_status = "已实名" if has_auth else "未实名"
                        print(f"实名状态: {auth_status} ({has_auth})")

                return data
            else:
                # HTML响应
                print("✅ Request successful!")
                print("Response is HTML content (account info page)")
                print(f"Content length: {len(response.text)} characters")

                # 从HTML中提取用户信息
                user_info = extract_user_info(response.text)
                if user_info:
                    print(f"\n📋 提取的用户信息:")
                    print(f"ID: {user_info.get('id', 'N/A')}")
                    print(f"昵称: {user_info.get('screen_name', 'N/A')}")
                    has_auth = user_info.get('has_already_authentication', None)
                    if has_auth is not None:
                        auth_status = "已实名" if has_auth else "未实名"
                        print(f"实名状态: {auth_status} ({has_auth})")
                else:
                    print("\n❌ 未能从HTML中提取到用户信息")

                return {
                    "html_content": response.text,
                    "content_type": content_type,
                    "user_info": user_info
                }
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            print("Response Text:", response.text)
            return None

    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")
        return None


# 直接调用示例（需要提供有效的sessionid）
if __name__ == "__main__":
    # 使用指定的sessionid值
    sessionid_value = "115da7a6d1c6d6779635c42ae1e3a6c4"
    print("获取账号详细信息:")
    result = get_account_info(sessionid=sessionid_value)