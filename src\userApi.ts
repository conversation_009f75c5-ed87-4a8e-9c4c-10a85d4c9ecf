import { registerUser, validate<PERSON><PERSON><PERSON>, getUser<PERSON>y<PERSON><PERSON>, useActivationCode, getUserCompleteInfo } from "./database";
import { encryptPassword, jsonResponse, errorResponse } from "./utils";
import type { Env } from './types';
import type { Request as WorkerRequest } from '@cloudflare/workers-types';

// 处理用户注册
export async function handleRegister(request: Request, env: Env): Promise<Response> {
	if (request.method !== "POST") {
		return errorResponse("方法不允许", 405);
	}
	
	try {
		const { phone, password } = await request.json<{ phone: string; password: string }>();
		
		// 验证输入
		if (!phone || !password) {
			return errorResponse("手机号和密码不能为空", 400);
		}
		
		// 密码加密
		const encryptedPassword = encryptPassword(password);
		
		// 注册用户
		const result = await registerUser(env.DB, phone, encryptedPassword);
		
		if (!result.success) {
			return errorResponse(result.message, 400);
		}
		
		return jsonResponse({
			success: true,
			message: "注册成功",
			userId: result.userId
		});
	} catch (error) {
		return errorResponse(`注册处理失败: ${error}`, 500);
	}
}

// 处理用户登录
export async function handleLogin(request: Request, env: Env): Promise<Response> {
	if (request.method !== "POST") {
		return errorResponse("方法不允许", 405);
	}
	
	try {
		const { phone, password, forceLogin, clientVersion } = await request.json<{ phone: string; password: string; forceLogin?: boolean; clientVersion?: string }>();
		
		// 验证输入
		if (!phone || !password) {
			return errorResponse("手机号和密码不能为空", 400);
		}
		
		// 如果提供了客户端版本，进行版本验证
		if (clientVersion) {
			// 从KV获取最新版本号
			const latestVersion = await env.ADMIN_KV.get('latest_client_version');
			
			// 如果存在最新版本号且与客户端版本不一致，返回需要升级的提示
			if (latestVersion && clientVersion !== latestVersion) {
				return jsonResponse({
					success: false,
					message: "客户端版本需要升级",
					needUpgrade: true,
					latestVersion: latestVersion
				});
			}
		}
		
		// 密码加密
		const encryptedPassword = encryptPassword(password);
		
		// 验证登录
		const loginResult = await validateLogin(env.DB, phone, encryptedPassword);
		
		if (!loginResult.success || !loginResult.user) {
			return errorResponse(loginResult.message, 401);
		}
		
		// 获取用户认证Durable Object
		const id = env.USER_AUTH_DO.idFromName("auth");
		const userAuthDO = env.USER_AUTH_DO.get(id);
		
		// 检查用户是否已登录
		const checkResponse = await userAuthDO.fetch(new Request("https://manbu.03310729.xyz/check-user-online", {
			method: "POST",
			headers: { "Content-Type": "application/json" },
			body: JSON.stringify({
				userId: loginResult.user.id,
				phone: loginResult.user.phone
			})
		}) as unknown as WorkerRequest);
		
		const checkResult = await checkResponse.json<{ success: boolean; online: boolean; userId?: number }>();
		
		// 如果用户已在线且没有强制登录标志，返回已在线状态
		if (checkResult.online && !forceLogin) {
			return jsonResponse({
				success: false,
				message: "账号已在其他设备登录",
				status: "online",
				requireForceLogin: true,
				userId: loginResult.user.id,
				phone: loginResult.user.phone
			});
		}

		// 获取完整用户信息
		const completeUserInfo = await getUserCompleteInfo(env.DB, loginResult.user.id);
		if (!completeUserInfo.success || !completeUserInfo.userInfo) {
			return errorResponse("获取用户信息失败", 500);
		}

		// 在Durable Object中创建会话
		const doResponse = await userAuthDO.fetch(new Request("https://manbu.03310729.xyz/login", {
			method: "POST",
			headers: { "Content-Type": "application/json" },
			body: JSON.stringify({
				userId: loginResult.user.id,
				phone: loginResult.user.phone,
				forceLogin: !!forceLogin,
				completeUserInfo: completeUserInfo.userInfo
			})
		}) as unknown as WorkerRequest);

		// 返回响应
		return doResponse as unknown as Response;
	} catch (error) {
		return errorResponse(`登录处理失败: ${error}`, 500);
	}
}

// 处理用户登出
export async function handleLogout(request: Request, env: Env): Promise<Response> {
	if (request.method !== "POST") {
		return errorResponse("方法不允许", 405);
	}
	
	try {
		const { sessionId } = await request.json<{ sessionId: string }>();
		
		if (!sessionId) {
			return errorResponse("会话ID不能为空", 400);
		}
		
		// 获取用户认证Durable Object
		const id = env.USER_AUTH_DO.idFromName("auth");
		const userAuthDO = env.USER_AUTH_DO.get(id);
		
		// 在Durable Object中处理登出
		const logoutResponse = await userAuthDO.fetch(new Request("https://manbu.03310729.xyz/logout", {
			method: "POST",
			headers: { "Content-Type": "application/json" },
			body: JSON.stringify({ sessionId })
		}) as unknown as WorkerRequest);
		
		return logoutResponse as unknown as Response;
	} catch (error) {
		return errorResponse(`登出处理失败: ${error}`, 500);
	}
}

// 检查会话状态
export async function handleCheckSession(request: Request, env: Env): Promise<Response> {
	if (request.method !== "POST") {
		return errorResponse("方法不允许", 405);
	}
	
	try {
		const { sessionId } = await request.json<{ sessionId: string }>();
		
		if (!sessionId) {
			return errorResponse("会话ID不能为空", 400);
		}
		
		// 使用Durable Object检查会话
		const id = env.USER_AUTH_DO.idFromName("auth");
		const userAuthDO = env.USER_AUTH_DO.get(id);
		
		// 在Durable Object中检查会话
		const response = await userAuthDO.fetch(new Request('https://manbu.03310729.xyz/check-session', {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({ sessionId })
		}) as unknown as WorkerRequest);
		
		// 获取响应内容
		const result = await response.json<{
			valid: boolean;
			message?: string;
			userId?: number;
			phone?: string;
		}>();

		if (!result.valid) {
			return errorResponse(result.message || "会话无效", 401);
		}

		// 返回会话信息
		return jsonResponse({
			success: true,
			message: "会话有效",
			userId: result.userId,
			phone: result.phone
		});
	} catch (error) {
		return errorResponse(`检查会话失败: ${error}`, 500);
	}
}

// 处理激活码使用
export async function handleActivateCode(request: Request, env: Env): Promise<Response> {
	if (request.method !== "POST") {
		return errorResponse("方法不允许", 405);
	}
	
	try {
		const { sessionId, code } = await request.json<{ sessionId: string; code: string }>();
		
		// 验证输入
		if (!sessionId || !code) {
			return errorResponse("会话ID和激活码不能为空", 400);
		}
		
		// 检查会话是否有效
		const id = env.USER_AUTH_DO.idFromName("auth");
		const userAuthDO = env.USER_AUTH_DO.get(id);
		
		// 验证会话
		const sessionResponse = await userAuthDO.fetch(new Request('https://manbu.03310729.xyz/check-session', {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({ sessionId })
		}) as unknown as WorkerRequest);
		
		const sessionResult = await sessionResponse.json<{ valid: boolean; userId?: number; phone?: string }>();
		
		if (!sessionResult.valid || !sessionResult.userId || !sessionResult.phone) {
			return errorResponse("无效的会话", 401);
		}
		
		// 使用激活码
		const result = await useActivationCode(env.DB, code, sessionResult.userId, sessionResult.phone);
		
		if (!result.success) {
			return errorResponse(result.message, 400);
		}
		
		return jsonResponse({
			success: true,
			message: result.message,
			days: result.days
		});
	} catch (error) {
		return errorResponse(`激活码处理失败: ${error}`, 500);
	}
}

// 处理SSE连接
export async function handleSSERequest(request: Request, env: Env): Promise<Response> {
	const url = new URL(request.url);
	const sessionId = url.searchParams.get("sessionId");

	if (!sessionId) {
		return errorResponse("缺少会话ID", 400);
	}

	const id = env.USER_AUTH_DO.idFromName("auth");
	const userAuthDO = env.USER_AUTH_DO.get(id);

	const workerRequest = request.clone() as unknown as WorkerRequest;
	const response = await userAuthDO.fetch(workerRequest);
	return response as unknown as Response;
}
