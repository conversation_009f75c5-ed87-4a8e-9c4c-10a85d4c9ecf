# 子账号API使用文档

## 概述

本文档详细介绍了子账号管理相关的API接口，包括子账号的创建、查询、删除以及子账号在平台账号管理方面的权限和操作。

## 重要说明

**如果子账号无法获取到平台账号（返回空数组）**，这通常不是API调用错误，而是**数据分配问题**：
- API调用成功：`success: true, accountInfos: []`
- 根本原因：数据库中没有 `current_holder_id` 等于该子账号ID的平台账号
- 解决方案：需要先为子账号分配平台账号（见本文档后面的详细说明）

基于演示页面的实际实现，子账号登录后会自动调用平台账号获取API，使用正确的参数格式。

## 基础概念

### 账号类型
- **主账号**：具有完整权限的账号，可以创建和管理子账号，管理所有平台账号
- **子账号**：归属于主账号的子级账号，权限受限，只能管理分配给自己的平台账号

### 权限体系
- 主账号可以创建、查看、删除子账号
- 主账号可以管理整个账号体系下的所有平台账号
- 子账号只能查看和管理分配给自己的平台账号（`current_holder_id` 等于自己的ID）
- 子账号不能创建其他子账号

---

## 子账号管理API

### 1. 创建子账号

**接口**: `POST /api/user/create-sub-account`

**描述**: 主账号为自己创建子账号

**权限要求**: 只有主账号可以创建子账号

**请求参数**:
```json
{
  "sessionId": "your-session-id",
  "ownerId": 123,
  "phone": "***********",
  "password": "sub_account_password"
}
```

**参数说明**:
- `sessionId`: 当前用户的会话ID（必填）
- `ownerId`: 主账号的用户ID（必填）
- `phone`: 子账号的手机号（必填，作为登录用户名）
- `password`: 子账号的密码（必填，建议6位以上）

**响应格式**:
```json
{
  "success": true,
  "message": "子账号创建成功",
  "subAccountId": 124
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "只有主账号才能创建子账号"
}
```

**示例代码**:
```python
import requests

def create_sub_account(session_id, owner_id, phone, password):
    """创建子账号"""
    url = f"{BASE_URL}/api/user/create-sub-account"
    data = {
        "sessionId": session_id,
        "ownerId": owner_id,
        "phone": phone,
        "password": password
    }
    
    response = requests.post(url, json=data)
    return response.json()

# 使用示例
result = create_sub_account("your-session-id", 123, "***********", "password123")
print(result)
```

---

### 2. 获取子账号列表

**接口**: `GET /api/user/sub-accounts`

**描述**: 获取指定主账号下的所有子账号

**权限要求**: 只有主账号可以查看子账号列表

**请求参数**:
- `sessionId`: 会话ID（查询参数，必填）
- `ownerId`: 主账号ID（查询参数，必填）

**示例**: `GET /api/user/sub-accounts?sessionId=xxx&ownerId=123`

**响应格式**:
```json
{
  "success": true,
  "subAccounts": [
    {
      "id": 124,
      "phone": "***********",
      "created_at": "2024-01-02T00:00:00.000Z",
      "last_login_at": "2024-07-18T09:00:00.000Z",
      "expiry_date": "2024-12-31T23:59:59.000Z"
    },
    {
      "id": 125,
      "phone": "***********",
      "created_at": "2024-01-03T00:00:00.000Z",
      "last_login_at": null,
      "expiry_date": "2024-12-31T23:59:59.000Z"
    }
  ]
}
```

**字段说明**:
- `id`: 子账号的用户ID
- `phone`: 子账号的手机号
- `created_at`: 创建时间
- `last_login_at`: 最后登录时间（null表示从未登录）
- `expiry_date`: 账号过期时间

**示例代码**:
```python
def get_sub_accounts(session_id, owner_id):
    """获取子账号列表"""
    url = f"{BASE_URL}/api/user/sub-accounts"
    params = {
        "sessionId": session_id,
        "ownerId": owner_id
    }

    response = requests.get(url, params=params)
    return response.json()

# 使用示例
result = get_sub_accounts("your-session-id", 123)
print(f"子账号数量: {len(result.get('subAccounts', []))}")
```

**JavaScript示例**:
```javascript
async function getSubAccounts(sessionId, ownerId) {
    try {
        const response = await fetch(`${API_URL}/api/user/sub-accounts?sessionId=${sessionId}&ownerId=${ownerId}`);
        const data = await response.json();

        if (data.success) {
            console.log('子账号列表:', data.subAccounts);
            return data.subAccounts;
        } else {
            console.error('获取子账号失败:', data.message);
            return [];
        }
    } catch (error) {
        console.error('网络错误:', error);
        return [];
    }
}
```

---

### 3. 删除子账号

**接口**: `POST /api/user/delete-sub-account`

**描述**: 删除指定的子账号

**权限要求**: 只有主账号可以删除自己的子账号

**请求参数**:
```json
{
  "sessionId": "your-session-id",
  "ownerId": 123,
  "subAccountId": 124
}
```

**参数说明**:
- `sessionId`: 当前用户的会话ID（必填）
- `ownerId`: 主账号的用户ID（必填）
- `subAccountId`: 要删除的子账号ID（必填）

**响应格式**:
```json
{
  "success": true,
  "message": "子账号删除成功"
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "子账号不存在或不属于该主账号"
}
```

**注意事项**:
- 删除子账号是不可逆操作
- 删除子账号时，该子账号持有的平台账号会保留在主账号体系中
- 建议在删除前先转移子账号持有的平台账号

**示例代码**:
```python
def delete_sub_account(session_id, owner_id, sub_account_id):
    """删除子账号"""
    url = f"{BASE_URL}/api/user/delete-sub-account"
    data = {
        "sessionId": session_id,
        "ownerId": owner_id,
        "subAccountId": sub_account_id
    }
    
    response = requests.post(url, json=data)
    return response.json()

# 使用示例
result = delete_sub_account("your-session-id", 123, 124)
print(result)
```

---

## 子账号登录

### 4. 子账号登录

**接口**: `POST /auth/login`

**描述**: 子账号使用与主账号相同的登录接口

**请求参数**:
```json
{
  "phone": "***********",
  "password": "sub_account_password",
  "forceLogin": false,
  "clientVersion": "1.0.0"
}
```

**响应格式**:
```json
{
  "success": true,
  "sessionId": "generated-session-id",
  "user": {
    "id": 124,
    "phone": "***********",
    "account_type": "子账号",
    "account_owner": 123,
    "created_at": "2024-01-02T00:00:00.000Z",
    "last_login_at": "2024-07-18T09:00:00.000Z"
  }
}
```

**重要字段**:
- `account_type`: 值为"子账号"
- `account_owner`: 指向主账号的ID

---

## 子账号平台账号管理

### 5. 获取平台账号列表（子账号视角）

**接口**: `GET /api/user/platform-accounts`

**描述**: 子账号获取分配给自己的平台账号列表

**请求参数**:
- `sessionId`: 会话ID（查询参数，必填）
- `user_id`: 子账号的用户ID（查询参数，必填）
- `is_main_account`: 设为false（查询参数，必填）

**示例**: `GET /api/user/platform-accounts?sessionId=xxx&user_id=124&is_main_account=false`

**响应格式**:
```json
{
  "success": true,
  "accountInfos": [
    {
      "phone": "***********",
      "username": "平台用户名",
      "login_type": "password",
      "team_tag": "团队A",
      "account_status": "active",
      "owner_id": 123,
      "current_holder_id": 124,
      "stats": {
        "followers": "1000",
        "total_reads": "50000",
        "total_income": "1500.00"
      },
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-07-18T10:00:00.000Z"
    }
  ]
}
```

**权限说明**:
- 子账号只能看到 `current_holder_id` 等于自己ID的平台账号
- 无法看到分配给其他子账号或主账号持有的平台账号

**重要提示**:
如果子账号无法获取到平台账号（返回空数组），最常见的原因是**没有平台账号分配给该子账号**。请检查以下几点：

1. **确认参数正确**: `user_id` 必须是子账号的ID，`is_main_account` 必须设为 `false`
2. **检查平台账号归属**: 确保有平台账号的 `current_holder_id` 等于该子账号的ID
3. **验证会话有效性**: 确保 `sessionId` 是有效的子账号会话
4. **查看错误信息**: 检查响应中的错误信息以了解具体问题

**最常见情况**：
- API调用成功，但 `accountInfos` 为空数组
- 这表示数据库中没有 `current_holder_id` 等于该子账号ID的平台账号
- 需要主账号先添加平台账号，然后转移给子账号，或者子账号自己添加平台账号

**调试示例**:
```javascript
// 正确的调用方式
const response = await fetch(`${API_URL}/api/user/platform-accounts?user_id=${subAccountId}&is_main_account=false&sessionId=${sessionId}`);
const data = await response.json();

if (!data.success) {
    console.error('获取失败:', data.message);
} else {
    console.log('获取到的平台账号数量:', data.accountInfos.length);
}
```

**完整的前端实现示例**（基于演示页面）:
```javascript
// 全局变量
let currentUser = null;
let sessionId = null;
let allPlatformAccounts = [];

// 加载平台账号列表（适用于主账号和子账号）
async function loadPlatformAccounts() {
    if (!currentUser || !currentUser.id) {
        console.error('用户未登录');
        return;
    }

    try {
        // 判断是否为主账号
        const isMainAccount = currentUser.account_type === '主账号';
        const response = await fetch(`${API_URL}/api/user/platform-accounts?user_id=${currentUser.id}&is_main_account=${isMainAccount}&sessionId=${sessionId}`);
        const data = await response.json();

        if (data.success) {
            allPlatformAccounts = data.accountInfos || [];
            displayPlatformAccounts(data.accountInfos || []);
            console.log(`${isMainAccount ? '主账号' : '子账号'}获取到 ${allPlatformAccounts.length} 个平台账号`);
        } else {
            console.error('获取平台账号失败:', data.message);
            showMessage(data.message || '获取平台账号失败', 'error');
            allPlatformAccounts = [];
            displayPlatformAccounts([]);
        }
    } catch (error) {
        console.error('获取平台账号失败:', error);
        showMessage('网络错误，无法获取平台账号信息', 'error');
        displayPlatformAccounts([]);
    }
}

// 显示平台账号列表
function displayPlatformAccounts(accounts) {
    const tbody = document.getElementById('platform-accounts-tbody');

    if (accounts.length === 0) {
        tbody.innerHTML = '<tr><td colspan="12" class="no-platform-accounts">暂无平台账号信息。</td></tr>';
        return;
    }

    const rowsHtml = accounts.map(account => {
        const isMainAccount = currentUser.account_type === '主账号';
        const canEdit = isMainAccount || account.current_holder_id === currentUser.id;
        const canTransfer = isMainAccount; // 只有主账号可以转移
        const canDelete = account.owner_id === currentUser.id; // 只能删除自己添加的

        return `
            <tr>
                <td><input type="checkbox" class="account-checkbox" data-phone="${account.phone}"></td>
                <td>${account.phone}</td>
                <td>${account.username || '未知'}</td>
                <td>${account.stats?.followers || '0'}</td>
                <td>${account.stats?.total_income || '0'}</td>
                <td>${account.stats?.yesterday_income || '0'}</td>
                <td>${account.stats?.yesterday_reads || '0'}</td>
                <td>
                    ${canEdit ? `<button onclick="editPlatformAccount('${account.phone}')" class="button button-small">编辑</button>` : ''}
                    ${canTransfer ? `<button onclick="transferPlatformAccount('${account.phone}')" class="button button-small">转移</button>` : ''}
                    ${canDelete ? `<button onclick="deletePlatformAccount('${account.phone}')" class="button button-small button-danger">删除</button>` : ''}
                </td>
            </tr>
        `;
    }).join('');

    tbody.innerHTML = rowsHtml;
}

// 用户登录后调用
function onUserLogin(user, session) {
    currentUser = user;
    sessionId = session;

    // 登录成功后自动加载平台账号
    loadPlatformAccounts();
}
```

---

### 6. 添加平台账号（子账号）

**接口**: `POST /api/user/platform-accounts`

**描述**: 子账号添加新的平台账号

**权限要求**: 子账号可以添加平台账号，但会归属到主账号体系中

**请求参数**:
```json
{
  "sessionId": "sub-account-session-id",
  "account_data": {
    "phone": "***********",
    "username": "新平台用户",
    "login_type": "password",
    "team_tag": "团队B",
    "account_status": "active",
    "stats": {
      "followers": "500",
      "total_reads": "10000",
      "total_income": "300.00"
    }
  },
  "current_holder_id": 124
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "平台账号添加成功"
}
```

**重要说明**:
- 子账号添加的平台账号，`owner_id` 会设置为子账号的ID
- `current_holder_id` 默认为子账号的ID
- 主账号可以看到子账号添加的所有平台账号

---

### 7. 更新平台账号（子账号）

**接口**: `PUT /api/user/platform-accounts`

**描述**: 子账号更新自己持有的平台账号信息

**权限限制**: 子账号只能更新 `current_holder_id` 等于自己ID的平台账号

**请求参数**:
```json
{
  "sessionId": "sub-account-session-id",
  "phone": "***********",
  "userId": 124,
  "isMainAccount": false,
  "accountData": {
    "username": "更新后的用户名",
    "team_tag": "新团队",
    "stats": {
      "followers": "600",
      "total_reads": "12000",
      "total_income": "350.00"
    }
  }
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "平台账号更新成功"
}
```

---

### 8. 删除平台账号（子账号）

**接口**: `DELETE /api/user/platform-accounts`

**描述**: 子账号删除自己添加的平台账号

**权限限制**: 子账号只能删除 `owner_id` 等于自己ID的平台账号

**请求参数**:
```json
{
  "sessionId": "sub-account-session-id",
  "phone": "***********",
  "userId": 124
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "平台账号删除成功"
}
```

---

## 权限限制总结

### 子账号不能执行的操作
1. **创建其他子账号**：只有主账号可以创建子账号
2. **查看其他子账号信息**：无法获取同级子账号的信息
3. **转移平台账号**：无法将平台账号转移给其他用户
4. **查看不属于自己的平台账号**：只能看到分配给自己的平台账号
5. **修改不属于自己的平台账号**：无法修改其他人持有的平台账号
6. **删除不是自己添加的平台账号**：只能删除自己添加的平台账号

### 子账号可以执行的操作
1. **正常登录和登出**：使用标准登录接口
2. **查看自己的用户信息**：获取个人账号详情
3. **添加平台账号**：向主账号体系添加新的平台账号
4. **查看分配给自己的平台账号**：查看持有的平台账号列表
5. **更新自己持有的平台账号**：修改平台账号信息
6. **删除自己添加的平台账号**：删除自己创建的平台账号
7. **接收实时通知**：通过SSE接收平台账号相关通知

---

## 错误处理

### 常见错误码
- `400`: 请求参数错误
- `401`: 会话无效或未登录
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 常见错误信息
- "只有主账号才能创建子账号"
- "只有主账号才能查看子账号列表"
- "子账号不存在或不属于该主账号"
- "没有权限操作此平台账号"
- "只有主账号可以转移平台账号"

---

## 最佳实践

### 1. 子账号管理
- 为子账号设置强密码
- 定期检查子账号的登录状态
- 合理分配平台账号给子账号
- 删除子账号前先转移其持有的平台账号

### 2. 平台账号分配
- 根据业务需要合理分配平台账号
- 定期检查平台账号的归属情况
- 使用转移功能重新分配平台账号

### 3. 权限控制
- 理解主账号和子账号的权限差异
- 在前端进行适当的权限检查
- 处理权限不足的错误情况

### 4. 数据同步
- 利用SSE实时通知功能保持数据同步
- 在操作完成后刷新相关数据
- 处理并发操作可能导致的数据冲突

---

## 注意事项

1. **会话管理**：子账号的会话管理与主账号相同，需要定期验证会话有效性
2. **数据一致性**：子账号的操作会影响整个主账号体系的数据
3. **实时通知**：子账号的操作会触发实时通知给主账号和相关用户
4. **权限验证**：所有API都会进行严格的权限验证
5. **数据安全**：敏感操作需要额外的确认步骤

---

## 故障排除

### 子账号无法获取平台账号的常见问题

#### 1. 返回空列表但没有错误（最常见问题）
**问题**: API调用成功，但 `accountInfos` 为空数组
**原因**: 没有平台账号分配给该子账号
**客户端日志示例**:
```
API响应解析成功: {'success': True, 'accountInfos': []}
启动同步获取到 0 个平台账号
```

**解决方案**:
1. **确认子账号ID正确**: 检查 `user_id=3` 是否是正确的子账号ID
2. **检查平台账号归属**: 在管理员后台查看是否有平台账号的 `current_holder_id` 等于 3
3. **添加平台账号**:
   - 方式1：主账号添加平台账号后转移给子账号
   - 方式2：子账号自己添加平台账号（会自动设置 `current_holder_id` 为子账号ID）

**验证步骤**:
```javascript
// 1. 确认子账号信息
console.log('子账号ID:', currentUser.id);
console.log('账号类型:', currentUser.account_type);
console.log('主账号ID:', currentUser.account_owner);

// 2. 检查API响应
const response = await fetch(`${API_URL}/api/user/platform-accounts?user_id=${currentUser.id}&is_main_account=false&sessionId=${sessionId}`);
const data = await response.json();
console.log('API响应:', data);
console.log('平台账号数量:', data.accountInfos?.length || 0);

// 3. 如果是空数组，检查主账号的平台账号（需要主账号权限）
if (data.accountInfos?.length === 0) {
    console.log('子账号没有平台账号，建议检查：');
    console.log('- 是否有平台账号的 current_holder_id 等于', currentUser.id);
    console.log('- 主账号是否已添加平台账号并转移给子账号');
    console.log('- 或者子账号是否需要自己添加平台账号');
}
```

**基于演示页面的完整登录流程**:
```javascript
// 演示页面的登录流程
async function login(phone, password) {
    const response = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ phone, password })
    });

    const data = await response.json();
    if (data.success) {
        sessionId = data.sessionId;

        // 关键：登录成功后，data.user 包含完整的用户信息
        if (data.user) {
            console.log('登录用户信息:', data.user);
            // data.user 包含: id, phone, account_type, account_owner, owner_phone 等

            // showLoggedInUser 会设置 currentUser 并自动调用 loadPlatformAccounts()
            showLoggedInUser(phone, data.user);
        }
    }
}

// showLoggedInUser 函数会：
function showLoggedInUser(phone, userInfo) {
    currentUser = userInfo; // 设置全局用户信息

    // 显示用户界面...

    // 自动加载平台账号
    loadPlatformAccounts(); // 这里会使用 currentUser.id 和 currentUser.account_type
}
```

#### 2. 参数错误
**问题**: 返回 "缺少用户ID" 或类似错误
**原因**: 请求参数不正确
**解决方案**:
```javascript
// 错误的调用方式
fetch(`/api/user/platform-accounts?sessionId=${sessionId}&userId=${userId}`)

// 正确的调用方式
fetch(`/api/user/platform-accounts?sessionId=${sessionId}&user_id=${userId}&is_main_account=false`)
```

#### 3. 会话无效
**问题**: 返回 401 错误或 "会话无效"
**原因**: sessionId 过期或无效
**解决方案**:
- 重新登录获取新的 sessionId
- 检查 sessionId 是否正确传递

#### 4. 权限不足
**问题**: 返回 403 错误或权限相关错误
**原因**: 尝试访问不属于自己的平台账号
**解决方案**:
- 确认子账号只能访问分配给自己的平台账号
- 检查平台账号的 `current_holder_id` 是否正确

#### 5. 数据不同步
**问题**: 前端显示的数据与实际不符
**原因**: 缓存或数据同步问题
**解决方案**:
- 在操作完成后重新调用 `loadPlatformAccounts()`
- 利用SSE实时通知更新数据
- 清除前端缓存

### 如何确认子账号是否真的有平台账号

如果您确信子账号应该有平台账号，但API返回空数组，请按以下步骤调试：

#### 步骤1：通过管理员后台检查
1. 登录管理员后台
2. 查看"平台账号管理"页面
3. 搜索该子账号的手机号或查看所有平台账号
4. 确认是否有平台账号的 `current_holder_id` 等于子账号ID

#### 步骤2：通过主账号检查
```javascript
// 使用主账号登录，查看所有平台账号
const response = await fetch(`${API_URL}/api/user/platform-accounts?user_id=${mainAccountId}&is_main_account=true&sessionId=${mainAccountSessionId}`);
const data = await response.json();

if (data.success) {
    console.log('主账号体系下的所有平台账号:');
    data.accountInfos.forEach(account => {
        console.log(`平台账号: ${account.phone}`);
        console.log(`  所有者ID: ${account.owner_id}`);
        console.log(`  当前持有者ID: ${account.current_holder_id}`);
        console.log(`  是否属于子账号ID=${subAccountId}:`, account.current_holder_id === subAccountId);
    });
}
```

#### 步骤3：检查数据库（如果有直接访问权限）
```sql
-- 查看子账号信息
SELECT id, phone, account_type, account_owner FROM users WHERE id = 3;

-- 查看主账号的平台账号数据
SELECT main_account_id, platform_accounts_data
FROM main_account_platform_data
WHERE main_account_id = (SELECT account_owner FROM users WHERE id = 3);
```

### 调试步骤

1. **检查用户信息**:
```javascript
console.log('当前用户:', currentUser);
console.log('账号类型:', currentUser.account_type);
console.log('用户ID:', currentUser.id);
```

2. **检查请求参数**:
```javascript
const isMainAccount = currentUser.account_type === '主账号';
console.log('是否主账号:', isMainAccount);
console.log('请求URL:', `${API_URL}/api/user/platform-accounts?user_id=${currentUser.id}&is_main_account=${isMainAccount}&sessionId=${sessionId}`);
```

3. **检查响应数据**:
```javascript
const response = await fetch(url);
const data = await response.json();
console.log('响应状态:', response.status);
console.log('响应数据:', data);
```

4. **检查平台账号归属**:
```javascript
if (data.success && data.accountInfos) {
    data.accountInfos.forEach(account => {
        console.log(`平台账号 ${account.phone}:`);
        console.log('  所有者ID:', account.owner_id);
        console.log('  当前持有者ID:', account.current_holder_id);
        console.log('  是否属于当前用户:', account.current_holder_id === currentUser.id);
    });
}
```

### 常见错误代码对照表

| 错误码 | 错误信息 | 原因 | 解决方案 |
|--------|----------|------|----------|
| 400 | 缺少用户ID | 请求参数错误 | 检查 `user_id` 参数 |
| 401 | 会话无效 | sessionId 过期 | 重新登录 |
| 403 | 权限不足 | 访问权限不够 | 检查账号类型和权限 |
| 404 | 资源不存在 | 用户或账号不存在 | 检查用户ID是否正确 |
| 500 | 服务器错误 | 后端处理异常 | 联系技术支持 |

---

## 如何为子账号分配平台账号

### 方式1：主账号添加后转移给子账号

1. **主账号添加平台账号**:
```javascript
// 主账号添加平台账号
const response = await fetch(`${API_URL}/api/user/platform-accounts`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        sessionId: mainAccountSessionId,
        account_data: {
            phone: "***********",
            username: "平台用户名",
            login_type: "password",
            account_status: "active",
            stats: {
                followers: "0",
                total_reads: "0",
                total_income: "0.00",
                yesterday_reads: "0",
                yesterday_income: "0.00",
                credit_score: "0"
            }
        }
    })
});
```

2. **主账号转移给子账号**:
```javascript
// 转移平台账号给子账号
const transferResponse = await fetch(`${API_URL}/api/user/transfer-platform-account`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        sessionId: mainAccountSessionId,
        phone: "***********",
        new_holder_id: 3, // 子账号ID
        user_id: 1 // 主账号ID
    })
});
```

### 方式2：子账号直接添加平台账号

```javascript
// 子账号直接添加平台账号（推荐方式）
const response = await fetch(`${API_URL}/api/user/platform-accounts`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        sessionId: subAccountSessionId,
        account_data: {
            phone: "***********",
            username: "子账号平台用户",
            login_type: "password",
            account_status: "active",
            stats: {
                followers: "0",
                total_reads: "0",
                total_income: "0.00",
                yesterday_reads: "0",
                yesterday_income: "0.00",
                credit_score: "0"
            }
        },
        current_holder_id: 3 // 子账号ID，可选，默认为当前用户ID
    })
});
```

### 验证分配结果

```javascript
// 验证子账号是否能获取到平台账号
async function verifySubAccountPlatformAccounts(subAccountId, sessionId) {
    const response = await fetch(`${API_URL}/api/user/platform-accounts?user_id=${subAccountId}&is_main_account=false&sessionId=${sessionId}`);
    const data = await response.json();

    if (data.success) {
        console.log(`子账号 ${subAccountId} 拥有 ${data.accountInfos.length} 个平台账号`);
        data.accountInfos.forEach(account => {
            console.log(`- 平台账号: ${account.phone}, 持有者ID: ${account.current_holder_id}`);
        });
    } else {
        console.error('获取失败:', data.message);
    }
}

// 使用示例
verifySubAccountPlatformAccounts(3, 'sub-account-session-id');
```

---

## 技术支持

如有问题，请联系技术支持团队或查看相关技术文档。

### 联系方式
- 技术文档: 查看完整的API文档
- 问题反馈: 提供详细的错误信息和调试日志
- 代码示例: 参考演示页面的实现方式

---

## 文档修正说明

根据实际客户端日志分析，本文档已修正以下内容：

1. **明确了子账号获取不到平台账号的根本原因**：
   - 不是API调用错误，而是数据库中没有分配给该子账号的平台账号
   - API会成功返回，但 `accountInfos` 为空数组

2. **添加了详细的故障排除步骤**：
   - 如何验证子账号ID是否正确
   - 如何检查平台账号的归属关系
   - 如何为子账号分配平台账号

3. **提供了完整的解决方案**：
   - 主账号添加后转移的方式
   - 子账号直接添加的方式（推荐）
   - 验证分配结果的方法

4. **基于演示页面的实际实现**：
   - 确认了正确的API调用方式
   - 提供了与演示页面一致的代码示例
