# 用户认证系统功能说明文档

## 项目概述

本项目是一个基于 Cloudflare Workers 和 Durable Objects 构建的用户认证系统，提供了完整的用户注册、登录、会话管理和后台管理功能。系统采用现代化的前后端分离架构，使用 SSE (Server-Sent Events) 实现实时通信，并通过 Cloudflare D1 数据库进行数据存储。

## 系统架构

### 核心组件

1. **Cloudflare Workers**: 作为系统的入口点，处理 HTTP 请求和路由分发
2. **Durable Objects**: 提供分布式状态管理，特别是用于用户会话和在线状态管理
3. **D1 数据库**: 提供持久化存储，用于存储用户数据和会话信息
4. **SSE (Server-Sent Events)**: 实现服务器到客户端的实时单向通信

### 文件结构

- `src/index.ts`: 主入口文件，处理请求路由和基础认证逻辑
- `src/userAuthDO.ts`: 用户认证 Durable Object，管理用户会话和在线状态
- `src/adminApi.ts`: 管理后台 API 实现
- `src/database.ts`: 数据库操作封装
- `src/utils.ts`: 工具函数集合
- `src/types.ts`: TypeScript 类型定义

## 功能模块

### 1. 用户认证模块

#### 1.1 用户注册

- **路径**: `/auth/register`
- **方法**: `POST`
- **功能**:
  - 接收用户手机号和密码
  - 验证手机号是否已被注册
  - 对密码进行加密处理
  - 创建用户记录并返回注册结果

#### 1.2 用户登录

- **路径**: `/auth/login`
- **方法**: `POST`
- **功能**:
  - 验证用户手机号和密码
  - 检查用户是否已在其他设备登录
  - 支持强制登录，将其他设备踢下线
  - 创建用户会话并返回会话 ID

#### 1.3 用户登出

- **路径**: `/auth/logout`
- **方法**: `POST`
- **功能**:
  - 验证用户会话
  - 清除用户会话记录
  - 断开相关 WebSocket 连接

#### 1.4 会话检查

- **路径**: `/auth/check-session`
- **方法**: `POST`
- **功能**:
  - 验证会话 ID 是否有效
  - 返回会话状态信息

### 2. SSE 实时通信模块

#### 2.1 SSE 连接

- **路径**: `/auth/sse`
- **查询参数**: `sessionId`, `clientId`
- **功能**:
  - 建立 SSE (Server-Sent Events) 连接
  - 通过会话 ID 验证用户身份
  - 维护用户在线状态

#### 2.2 实时消息

系统支持的 SSE 消息类型:
- 连接成功消息
- 强制下线通知
- 系统公告
- 会话状态变更通知

### 3. 管理后台 API

#### 3.1 获取账号列表

- **路径**: `/api/admin/accounts`
- **方法**: `GET`
- **查询参数**: `phone` (可选，用于按手机号搜索)
- **功能**:
  - 获取系统中所有用户账号的列表
  - 支持按手机号模糊搜索
  - 返回用户注册时间、最后登录时间、在线状态等信息

#### 3.2 获取登录状态

- **路径**: `/api/admin/login-status`
- **方法**: `GET`
- **功能**:
  - 获取当前所有在线用户的会话信息
  - 包括手机号、登录时间、设备信息等

#### 3.3 删除账号

- **路径**: `/api/admin/delete-account`
- **方法**: `POST`
- **功能**:
  - 删除指定手机号的用户账号
  - 删除关联的会话记录

#### 3.4 强制用户下线

- **路径**: `/api/admin/force-logout`
- **方法**: `POST`
- **功能**:
  - 强制指定手机号的用户下线
  - 通过 WebSocket 通知用户被强制下线
  - 清除相关会话记录

### 4. 数据库结构

#### 4.1 用户表 (users)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER | 主键，自增 |
| phone | TEXT | 用户手机号，唯一 |
| password | TEXT | 加密后的密码 |
| created_at | TEXT | 注册时间 |
| last_login_at | TEXT | 最后登录时间 |
| expiry_date | TEXT | 账号过期时间 |

## 状态管理

### 会话管理

系统采用 Durable Object 进行分布式会话管理，具体信息包括:

1. **用户会话**:
   - 用户ID
   - 手机号
   - 会话ID
   - 最后活跃时间
   - 客户端连接ID集合

2. **索引映射**:
   - 手机号到用户ID的映射
   - 会话ID到用户ID的映射

### SSE 连接管理

系统维护 SSE 连接列表，支持以下操作:

1. 建立新的 SSE 连接
2. 发送消息到指定连接
3. 广播消息到用户的所有连接
4. 处理连接关闭和断开检测

## 部署信息

本项目部署在 Cloudflare Workers 平台上，使用到的 Cloudflare 资源包括:

1. **Workers**: 处理 HTTP 请求和执行业务逻辑
2. **Durable Objects**: 状态管理
3. **D1 数据库**: 数据存储
4. **SSE (Server-Sent Events)**: 实时通信

## 安全性考虑

1. 密码采用加密存储
2. 会话验证机制防止未授权访问
3. 强制登录功能防止账号同时在多处登录
4. 管理API访问控制

## API文档

详细的API文档请参考 [API.md](API.md) 文件。

## 使用方法

### 1. 本地开发

```bash
# 启动开发服务器
npm run dev
```

### 2. 部署到 Cloudflare

```bash
# 部署到 Cloudflare Workers
npm run deploy
```

### 3. 生成 TypeScript 类型

```bash
# 生成 Cloudflare Workers 类型
npm run cf-typegen
``` 