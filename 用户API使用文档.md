# 用户API使用文档

## 概述

本文档详细说明了用户认证系统的API接口使用方法。系统基于Cloudflare Workers构建，提供完整的用户注册、登录、会话管理和账户管理功能。

**API基础URL**: `https://manbu.********.xyz`

## 认证机制

系统使用会话ID（sessionId）进行用户身份验证。除了注册和登录接口外，所有用户API都需要提供有效的sessionId。

### 会话ID传递方式
- **GET请求**: 通过查询参数传递 `?sessionId=xxx`
- **POST/PUT/DELETE请求**: 在请求体中包含 `sessionId` 字段

## API接口分类

### 1. 公开认证接口 (`/auth/`)
这些接口不需要会话验证，用于用户注册、登录等基础认证操作。

### 2. 用户API接口 (`/api/user/`)
这些接口需要有效的会话ID，用于用户账户管理和平台账号操作。

### 3. SSE实时通信 (`/auth/sse`)
用于建立服务器推送连接，接收实时消息。

---

## 详细API接口

### 1. 用户注册

**接口**: `POST /auth/register`

**描述**: 注册新用户账号

**请求参数**:
```json
{
  "phone": "***********",
  "password": "your_password"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "注册成功",
  "userId": 123
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "手机号已注册"
}
```

**状态码**:
- `200`: 注册成功
- `400`: 参数错误或手机号已注册
- `405`: 请求方法不允许
- `500`: 服务器内部错误

---

### 2. 用户登录

**接口**: `POST /auth/login`

**描述**: 用户登录获取会话ID

**请求参数**:
```json
{
  "phone": "***********",
  "password": "your_password",
  "forceLogin": false,
  "clientVersion": "1.0.0"
}
```

**参数说明**:
- `phone`: 手机号（必填）
- `password`: 密码（必填）
- `forceLogin`: 是否强制登录，踢掉其他设备（可选，默认false）
- `clientVersion`: 客户端版本号（可选，用于版本检查）

**成功响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "sessionId": "uuid-session-id",
  "userInfo": {
    "id": 123,
    "phone": "***********",
    "account_type": "主账号",
    "expiry_date": "2024-12-31T23:59:59.000Z"
  }
}
```

**需要强制登录响应**:
```json
{
  "success": false,
  "message": "账号已在其他设备登录",
  "requireForceLogin": true
}
```

**手机号冲突响应**:
```json
{
  "success": false,
  "message": "手机号已被其他账号占用",
  "requireForceLogin": true
}
```

**版本升级响应**:
```json
{
  "success": false,
  "message": "客户端版本需要升级",
  "needUpgrade": true,
  "latestVersion": "1.1.0"
}
```

---

### 3. 用户登出

**接口**: `POST /auth/logout`

**描述**: 用户登出，销毁会话

**请求参数**:
```json
{
  "sessionId": "your-session-id"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "登出成功",
  "userId": 123
}
```

---

### 4. 会话检查

**接口**: `POST /auth/check-session`

**描述**: 检查会话是否有效

**请求参数**:
```json
{
  "sessionId": "your-session-id"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "会话有效",
  "userId": 123,
  "phone": "***********"
}
```

---

### 5. 激活码使用

**接口**: `POST /auth/activate-code`

**描述**: 使用激活码延长账号有效期

**请求参数**:
```json
{
  "sessionId": "your-session-id",
  "code": "ACTIVATION-CODE-123"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "成功激活30天会员",
  "days": 30
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "激活码不存在"
}
```

```json
{
  "success": false,
  "message": "激活码已被使用"
}
```

```json
{
  "success": false,
  "message": "每个账号只能使用一次1天激活码"
}
```

---

### 6. 获取用户信息

**接口**: `GET /api/user/user-info`

**描述**: 获取指定用户的详细信息

**请求参数**:
- `sessionId`: 会话ID（查询参数）
- `phone`: 要查询的手机号（查询参数）

**示例**: `GET /api/user/user-info?sessionId=xxx&phone=***********`

**响应格式**:
```json
{
  "success": true,
  "user": {
    "id": 123,
    "phone": "***********",
    "account_type": "主账号",
    "account_owner": null,
    "owner_phone": null,
    "created_at": "2024-01-01T00:00:00.000Z",
    "last_login_at": "2024-07-18T10:00:00.000Z",
    "expiry_date": "2024-12-31T23:59:59.000Z"
  }
}
```

---

### 7. 获取子账号列表

**接口**: `GET /api/user/sub-accounts`

**描述**: 获取指定主账号下的所有子账号

**请求参数**:
- `sessionId`: 会话ID（查询参数）
- `ownerId`: 主账号ID（查询参数）

**示例**: `GET /api/user/sub-accounts?sessionId=xxx&ownerId=123`

**响应格式**:
```json
{
  "success": true,
  "accounts": [
    {
      "id": 124,
      "phone": "***********",
      "registerTime": "2024-01-02T00:00:00.000Z",
      "lastLoginTime": "2024-07-18T09:00:00.000Z",
      "isOnline": false,
      "expiryDate": "2024-12-31T23:59:59.000Z",
      "accountType": "子账号",
      "accountOwner": 123,
      "ownerPhone": "***********"
    }
  ]
}
```

---

### 8. 创建子账号

**接口**: `POST /api/user/create-sub-account`

**描述**: 为主账号创建子账号

**请求参数**:
```json
{
  "sessionId": "your-session-id",
  "ownerId": 123,
  "phone": "***********",
  "password": "sub_account_password"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "子账号创建成功",
  "userId": 124
}
```

---

### 9. 删除子账号

**接口**: `POST /api/user/delete-sub-account`

**描述**: 删除指定的子账号

**请求参数**:
```json
{
  "sessionId": "your-session-id",
  "ownerId": 123,
  "subAccountId": 124
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "子账号删除成功"
}
```

---

### 10. SSE实时连接

**接口**: `GET /auth/sse`

**描述**: 建立Server-Sent Events连接，接收实时推送消息

**请求参数**:
- `sessionId`: 会话ID（查询参数，必填）
- `clientId`: 客户端唯一标识（查询参数，可选，不提供时自动生成）

**示例**:
- `GET /auth/sse?sessionId=xxx&clientId=client-123`
- `GET /auth/sse?sessionId=xxx` （自动生成clientId）

**响应**: 返回SSE流，持续推送消息

**消息格式**:
```
data: {"type":"force_logout","message":"您的账号已在另一处登录 (强制)","timestamp":"2024-07-19T10:00:00.000Z"}

data: {"type":"force_logout","message":"您的手机号已被用于新的登录会话","timestamp":"2024-07-19T10:00:00.000Z"}

data: {"type":"force_logout","message":"您已被管理员强制下线","timestamp":"2024-07-19T10:00:00.000Z"}

data: {"type":"force_logout","message":"您已成功退出登录","timestamp":"2024-07-19T10:00:00.000Z"}

data: {"type":"platform_account_received","message":"您添加了新的平台账号","data":{...}}

data: {"type":"platform_account_notification","message":"平台账号状态更新","data":{...}}
```

---

## 平台账号管理API

### 11. 获取平台账号列表

**接口**: `GET /api/user/platform-accounts`

**描述**: 获取用户的平台账号信息列表

**请求参数**:
- `sessionId`: 会话ID（查询参数）
- `user_id`: 用户ID（查询参数）
- `is_main_account`: 是否为主账号（查询参数，可选）
- `phone`: 指定平台账号手机号（查询参数，可选）

**示例**: `GET /api/user/platform-accounts?sessionId=xxx&user_id=123&is_main_account=true`

**响应格式**:
```json
{
  "success": true,
  "accountInfos": [
    {
      "phone": "***********",
      "username": "用户名",
      "password": "密码",
      "owner_id": 123,
      "current_holder_id": 123,
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### 12. 添加平台账号

**接口**: `POST /api/user/platform-accounts`

**描述**: 添加新的平台账号

**请求参数**:
```json
{
  "sessionId": "your-session-id",
  "account_data": {
    "phone": "***********",
    "username": "用户名",
    "password": "密码"
  },
  "current_holder_id": 123
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "平台账号添加成功"
}
```

### 13. 更新平台账号

**接口**: `PUT /api/user/platform-accounts`

**描述**: 更新平台账号信息

**请求参数**:
```json
{
  "sessionId": "your-session-id",
  "phone": "***********",
  "userId": 123,
  "isMainAccount": true,
  "accountData": {
    "username": "新用户名",
    "password": "新密码"
  },
  "currentHolderId": 123
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "平台账号更新成功"
}
```

### 14. 删除平台账号

**接口**: `DELETE /api/user/platform-accounts`

**描述**: 删除指定的平台账号

**请求参数**:
```json
{
  "sessionId": "your-session-id",
  "phone": "***********",
  "userId": 123
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "平台账号删除成功"
}
```

### 15. 转移平台账号

**接口**: `POST /api/user/transfer-platform-account`

**描述**: 将平台账号转移给其他用户

**请求参数**:
```json
{
  "sessionId": "your-session-id",
  "phone": "***********",
  "new_holder_id": 124,
  "user_id": 123
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "平台账号转移成功"
}
```

### 16. 批量转移平台账号

**接口**: `POST /api/user/batch-transfer-platform-accounts`

**描述**: 批量转移多个平台账号给指定用户

**请求参数**:
```json
{
  "sessionId": "your-session-id",
  "user_id": 123,
  "transfers": [
    {
      "phone": "***********",
      "newHolderId": 124
    },
    {
      "phone": "***********",
      "newHolderId": 125
    }
  ]
}
```

**参数说明**:
- `sessionId`: 会话ID（必填）
- `user_id`: 操作者用户ID，必须是主账号（必填）
- `transfers`: 转移操作数组（必填）
  - `phone`: 要转移的平台账号手机号（必填）
  - `newHolderId`: 新持有者的用户ID（必填）

**成功响应**:
```json
{
  "success": true,
  "message": "批量转移完成，成功: 1, 失败: 1",
  "results": [
    {
      "phone": "***********",
      "success": true,
      "message": "转移成功"
    },
    {
      "phone": "***********",
      "success": false,
      "message": "目标用户不存在"
    }
  ],
  "successCount": 1,
  "failCount": 1
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "只有主账号可以转移平台账号"
}
```

```json
{
  "success": false,
  "message": "缺少用户ID或转移数据"
}
```

---

## 错误处理

### 通用错误响应格式
```json
{
  "success": false,
  "message": "错误描述信息"
}
```

### 常见错误状态码
- `400`: 请求参数错误
- `401`: 会话无效或未授权
- `403`: 权限不足
- `404`: 接口不存在或资源不存在
- `405`: 请求方法不允许
- `500`: 服务器内部错误

### 会话相关错误
- "缺少会话ID"
- "会话无效"
- "会话不存在或已失效"
- "无法获取用户ID"

### 用户认证相关错误
- "手机号和密码不能为空"
- "手机号已注册"
- "手机号或密码不正确"
- "账号已在其他设备登录"
- "手机号已被其他账号占用"
- "客户端版本需要升级"

### 强制登出相关错误
- "用户不在线或未找到"
- "强制下线处理失败"

### 子账号管理相关错误
- "缺少主账号ID"
- "只有主账号才能查看子账号列表"
- "主账号不存在"

### 平台账号管理相关错误
- "缺少用户ID"
- "缺少手机号参数"
- "平台账号不存在"
- "权限不足，无法操作此平台账号"

### 激活码相关错误
- "会话ID和激活码不能为空"
- "激活码不存在"
- "激活码已被使用"
- "每个账号只能使用一次1天激活码"

---

## 强制登出机制详解

系统实现了完善的强制登出机制，用于处理多设备登录冲突和管理员强制下线等场景。

### 触发场景

1. **同一用户多设备登录**
   - 当用户在新设备登录时，如果该用户已在其他设备登录
   - 不使用 `forceLogin=true` 时，返回 `requireForceLogin: true`
   - 使用 `forceLogin=true` 时，强制踢掉其他设备

2. **手机号冲突**
   - 当不同用户尝试使用同一手机号登录时
   - 系统会检测到手机号已被其他账号占用
   - 需要使用强制登录来解决冲突

3. **管理员强制下线**
   - 管理员可以通过后台强制用户下线
   - 用户会立即收到强制登出消息

4. **用户主动登出**
   - 用户调用登出接口时，会通知所有设备

### 强制登出流程

1. **检测冲突**: 系统检测到登录冲突或收到强制下线指令
2. **发送通知**: 通过SSE连接向目标用户的所有设备发送 `force_logout` 消息
3. **清理会话**: 清除服务器端的用户会话数据
4. **更新状态**: 通知管理员用户下线状态变更

### SSE强制登出消息类型

```json
{
  "type": "force_logout",
  "message": "具体的登出原因",
  "timestamp": "2024-07-19T10:00:00.000Z"
}
```

**常见的登出原因**:
- "您的账号已在另一处登录 (强制)" - 同一用户强制登录
- "您的手机号已被用于新的登录会话" - 手机号冲突强制登录
- "您已被管理员强制下线" - 管理员强制下线
- "您已成功退出登录" - 用户主动登出

### 客户端处理建议

```javascript
// 监听SSE强制登出消息
eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);

  if (data.type === 'force_logout') {
    // 清理本地存储的会话信息
    localStorage.removeItem('sessionId');

    // 显示登出原因
    alert(data.message);

    // 跳转到登录页面
    window.location.href = '/login';

    // 关闭SSE连接
    eventSource.close();
  }
};
```

---

## 使用示例

### JavaScript/TypeScript示例

```javascript
// 用户注册
async function registerUser(phone, password) {
  const response = await fetch('https://manbu.********.xyz/auth/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ phone, password })
  });
  
  return await response.json();
}

// 用户登录
async function loginUser(phone, password, forceLogin = false) {
  const response = await fetch('https://manbu.********.xyz/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ phone, password, forceLogin })
  });
  
  return await response.json();
}

// 获取用户信息
async function getUserInfo(sessionId, phone) {
  const response = await fetch(
    `https://manbu.********.xyz/api/user/user-info?sessionId=${sessionId}&phone=${phone}`
  );

  return await response.json();
}

// 获取平台账号列表
async function getPlatformAccounts(sessionId, userId, isMainAccount = true) {
  const response = await fetch(
    `https://manbu.********.xyz/api/user/platform-accounts?sessionId=${sessionId}&user_id=${userId}&is_main_account=${isMainAccount}`
  );

  return await response.json();
}

// 添加平台账号
async function addPlatformAccount(sessionId, accountData, currentHolderId) {
  const response = await fetch('https://manbu.********.xyz/api/user/platform-accounts', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      sessionId,
      account_data: accountData,
      current_holder_id: currentHolderId
    })
  });

  return await response.json();
}

// 使用激活码
async function useActivationCode(sessionId, code) {
  const response = await fetch('https://manbu.********.xyz/auth/activate-code', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ sessionId, code })
  });

  return await response.json();
}

// 建立SSE连接
function connectSSE(sessionId, clientId) {
  const eventSource = new EventSource(
    `https://manbu.********.xyz/auth/sse?sessionId=${sessionId}&clientId=${clientId}`
  );
  
  eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('收到消息:', data);
  };
  
  eventSource.onerror = function(event) {
    console.error('SSE连接错误:', event);
  };
  
  return eventSource;
}
```

### Python示例

```python
import requests
import json

BASE_URL = "https://manbu.********.xyz"

def register_user(phone, password):
    """用户注册"""
    url = f"{BASE_URL}/auth/register"
    data = {"phone": phone, "password": password}
    
    response = requests.post(url, json=data)
    return response.json()

def login_user(phone, password, force_login=False):
    """用户登录"""
    url = f"{BASE_URL}/auth/login"
    data = {
        "phone": phone, 
        "password": password, 
        "forceLogin": force_login
    }
    
    response = requests.post(url, json=data)
    return response.json()

def get_user_info(session_id, phone):
    """获取用户信息"""
    url = f"{BASE_URL}/api/user/user-info"
    params = {"sessionId": session_id, "phone": phone}
    
    response = requests.get(url, params=params)
    return response.json()

def create_sub_account(session_id, owner_id, phone, password):
    """创建子账号"""
    url = f"{BASE_URL}/api/user/create-sub-account"
    data = {
        "sessionId": session_id,
        "ownerId": owner_id,
        "phone": phone,
        "password": password
    }

    response = requests.post(url, json=data)
    return response.json()

def get_platform_accounts(session_id, user_id, is_main_account=True):
    """获取平台账号列表"""
    url = f"{BASE_URL}/api/user/platform-accounts"
    params = {
        "sessionId": session_id,
        "user_id": user_id,
        "is_main_account": str(is_main_account).lower()
    }

    response = requests.get(url, params=params)
    return response.json()

def add_platform_account(session_id, account_data, current_holder_id):
    """添加平台账号"""
    url = f"{BASE_URL}/api/user/platform-accounts"
    data = {
        "sessionId": session_id,
        "account_data": account_data,
        "current_holder_id": current_holder_id
    }

    response = requests.post(url, json=data)
    return response.json()

def use_activation_code(session_id, code):
    """使用激活码"""
    url = f"{BASE_URL}/auth/activate-code"
    data = {
        "sessionId": session_id,
        "code": code
    }

    response = requests.post(url, json=data)
    return response.json()
```

---

## 注意事项

1. **会话管理**: sessionId具有时效性，建议定期调用会话检查接口验证有效性
2. **并发登录**: 系统支持强制登录功能，可踢掉其他设备的登录会话
3. **版本控制**: 登录时可传入客户端版本号，系统会检查是否需要升级
4. **实时通信**: 使用SSE连接可接收账号状态变更、强制登出等实时消息
5. **错误处理**: 所有API都返回统一的错误格式，便于客户端处理
6. **安全性**: 密码在传输前会进行加密处理，建议使用HTTPS协议
7. **平台账号管理**: 平台账号以手机号作为唯一标识符，支持转移和批量操作
8. **子账号权限**: 只有主账号可以创建和管理子账号
9. **激活码限制**: 1天激活码每个账号只能使用一次
10. **SSE连接**: clientId参数可选，不提供时系统会自动生成唯一标识

---

## 更新日志

- **v1.0.0**: 初始版本，包含基础用户认证和账号管理功能
  - 支持用户注册、登录、登出
  - 支持会话管理和检查
  - 支持子账号创建、删除和列表查询
  - 支持平台账号完整CRUD操作
  - 支持平台账号转移和批量转移
  - 支持SSE实时通信和消息推送
  - 支持激活码生成和使用
  - 支持客户端版本检查
  - 支持强制登录和多设备管理

---

## 技术支持

如有API使用问题，请联系技术支持团队或查看项目文档。
